/**
 * Website Chatbot Widget
 * Embeddable chatbot for any website
 */

(function() {
    'use strict';
    
    // Configuration
    const WIDGET_CONFIG = {
        apiBaseUrl: window.location.protocol + '//' + window.location.host,
        widgetId: null,
        chatbotId: null,
        sessionId: null,
        conversationId: null,
        isOpen: false,
        isMinimized: false,
        messageQueue: [],
        typingIndicator: false
    };
    
    // Initialize widget
    function initWidget() {
        // Get chatbot ID from script URL
        const scriptElement = document.currentScript || document.querySelector('script[src*="widget.js"]');
        if (scriptElement) {
            const urlParams = new URLSearchParams(scriptElement.src.split('?')[1]);
            WIDGET_CONFIG.chatbotId = urlParams.get('id');
            WIDGET_CONFIG.widgetId = `chatbot-widget-${WIDGET_CONFIG.chatbotId}`;
        }
        
        if (!WIDGET_CONFIG.chatbotId) {
            console.error('Chatbot ID not found in widget URL');
            return;
        }
        
        // Generate session ID
        WIDGET_CONFIG.sessionId = generateSessionId();
        
        // Create widget HTML
        createWidgetHTML();
        
        // Add event listeners
        addEventListeners();
        
        // Load chatbot configuration
        loadChatbotConfig();
        
        // Initialize conversation
        initConversation();
        
        console.log('Website chatbot widget initialized:', WIDGET_CONFIG.chatbotId);
    }
    
    // Create widget HTML
    function createWidgetHTML() {
        const widgetHTML = `
            <div id="${WIDGET_CONFIG.widgetId}" class="chatbot-widget-container">
                <!-- Chatbot Toggle Button -->
                <div class="chatbot-toggle" id="chatbot-toggle-${WIDGET_CONFIG.chatbotId}">
                    <div class="chatbot-toggle-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2ZM20 16H6L4 18V4H20V16Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <div class="chatbot-toggle-badge" id="chatbot-badge-${WIDGET_CONFIG.chatbotId}" style="display: none;">0</div>
                </div>
                
                <!-- Chatbot Panel -->
                <div class="chatbot-panel" id="chatbot-panel-${WIDGET_CONFIG.chatbotId}">
                    <!-- Header -->
                    <div class="chatbot-header">
                        <div class="chatbot-header-info">
                            <div class="chatbot-title" id="chatbot-title-${WIDGET_CONFIG.chatbotId}">Chat with us</div>
                            <div class="chatbot-subtitle" id="chatbot-subtitle-${WIDGET_CONFIG.chatbotId}">We're here to help</div>
                        </div>
                        <div class="chatbot-header-actions">
                            <button class="chatbot-minimize" id="chatbot-minimize-${WIDGET_CONFIG.chatbotId}" title="Minimize">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M14 8H2" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                            </button>
                            <button class="chatbot-close" id="chatbot-close-${WIDGET_CONFIG.chatbotId}" title="Close">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 4L4 12M4 4L12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Messages Container -->
                    <div class="chatbot-messages" id="chatbot-messages-${WIDGET_CONFIG.chatbotId}">
                        <!-- Messages will be added here -->
                    </div>
                    
                    <!-- Input Area -->
                    <div class="chatbot-input-area">
                        <div class="chatbot-input-wrapper">
                            <textarea 
                                class="chatbot-input" 
                                id="chatbot-input-${WIDGET_CONFIG.chatbotId}"
                                placeholder="Type your message..."
                                rows="1"
                            ></textarea>
                            <button class="chatbot-send" id="chatbot-send-${WIDGET_CONFIG.chatbotId}" disabled>
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M10 0L20 10L10 20L8.5 18.5L16.5 10.5H0V9.5H16.5L8.5 1.5L10 0Z" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                        <div class="chatbot-typing" id="chatbot-typing-${WIDGET_CONFIG.chatbotId}" style="display: none;">
                            <div class="typing-indicator">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                            <span class="typing-text">AI is typing...</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Add widget to page
        const widgetContainer = document.getElementById(WIDGET_CONFIG.widgetId);
        if (widgetContainer) {
            widgetContainer.innerHTML = widgetHTML;
        } else {
            // Create container if it doesn't exist
            const newContainer = document.createElement('div');
            newContainer.innerHTML = widgetHTML;
            document.body.appendChild(newContainer.firstElementChild);
        }
        
        // Add CSS styles
        addWidgetStyles();
    }
    
    // Add widget CSS styles
    function addWidgetStyles() {
        if (document.getElementById('chatbot-widget-styles')) {
            return; // Styles already added
        }
        
        const styles = `
            <style id="chatbot-widget-styles">
                .chatbot-widget-container {
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    z-index: 9999;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                }
                
                .chatbot-toggle {
                    width: 60px;
                    height: 60px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                    transition: all 0.3s ease;
                    position: relative;
                }
                
                .chatbot-toggle:hover {
                    transform: scale(1.1);
                    box-shadow: 0 6px 25px rgba(0,0,0,0.2);
                }
                
                .chatbot-toggle-icon {
                    color: white;
                }
                
                .chatbot-toggle-badge {
                    position: absolute;
                    top: -5px;
                    right: -5px;
                    background: #ff4757;
                    color: white;
                    border-radius: 50%;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 12px;
                    font-weight: bold;
                }
                
                .chatbot-panel {
                    position: absolute;
                    bottom: 80px;
                    right: 0;
                    width: 350px;
                    height: 500px;
                    background: white;
                    border-radius: 16px;
                    box-shadow: 0 10px 40px rgba(0,0,0,0.15);
                    display: none;
                    flex-direction: column;
                    overflow: hidden;
                }
                
                .chatbot-panel.open {
                    display: flex;
                }
                
                .chatbot-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 20px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .chatbot-title {
                    font-size: 18px;
                    font-weight: 600;
                    margin-bottom: 4px;
                }
                
                .chatbot-subtitle {
                    font-size: 14px;
                    opacity: 0.9;
                }
                
                .chatbot-header-actions {
                    display: flex;
                    gap: 8px;
                }
                
                .chatbot-header-actions button {
                    background: none;
                    border: none;
                    color: white;
                    cursor: pointer;
                    padding: 4px;
                    border-radius: 4px;
                    transition: background 0.2s ease;
                }
                
                .chatbot-header-actions button:hover {
                    background: rgba(255,255,255,0.1);
                }
                
                .chatbot-messages {
                    flex: 1;
                    padding: 20px;
                    overflow-y: auto;
                    display: flex;
                    flex-direction: column;
                    gap: 16px;
                }
                
                .chatbot-message {
                    display: flex;
                    gap: 12px;
                    animation: fadeInUp 0.3s ease;
                }
                
                .chatbot-message.user {
                    flex-direction: row-reverse;
                }
                
                .chatbot-message-avatar {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    font-weight: 600;
                    color: white;
                }
                
                .chatbot-message.user .chatbot-message-avatar {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }
                
                .chatbot-message.assistant .chatbot-message-avatar {
                    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                }
                
                .chatbot-message-content {
                    max-width: 70%;
                    padding: 12px 16px;
                    border-radius: 18px;
                    font-size: 14px;
                    line-height: 1.4;
                }
                
                .chatbot-message.user .chatbot-message-content {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }
                
                .chatbot-message.assistant .chatbot-message-content {
                    background: #f8f9fa;
                    color: #333;
                    border: 1px solid #e9ecef;
                }
                
                .chatbot-input-area {
                    padding: 20px;
                    border-top: 1px solid #e9ecef;
                }
                
                .chatbot-input-wrapper {
                    display: flex;
                    gap: 12px;
                    align-items: flex-end;
                }
                
                .chatbot-input {
                    flex: 1;
                    border: 1px solid #e9ecef;
                    border-radius: 20px;
                    padding: 12px 16px;
                    font-size: 14px;
                    resize: none;
                    outline: none;
                    transition: border-color 0.2s ease;
                    font-family: inherit;
                }
                
                .chatbot-input:focus {
                    border-color: #667eea;
                }
                
                .chatbot-send {
                    width: 40px;
                    height: 40px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border: none;
                    border-radius: 50%;
                    color: white;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.2s ease;
                }
                
                .chatbot-send:hover:not(:disabled) {
                    transform: scale(1.05);
                }
                
                .chatbot-send:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
                
                .chatbot-typing {
                    margin-top: 12px;
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    color: #6c757d;
                    font-size: 14px;
                }
                
                .typing-indicator {
                    display: flex;
                    gap: 4px;
                }
                
                .typing-indicator span {
                    width: 8px;
                    height: 8px;
                    background: #6c757d;
                    border-radius: 50%;
                    animation: typing 1.4s infinite ease-in-out;
                }
                
                .typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
                .typing-indicator span:nth-child(2) { animation-delay: -0.16s; }
                
                @keyframes typing {
                    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
                    40% { transform: scale(1); opacity: 1; }
                }
                
                @keyframes fadeInUp {
                    from { opacity: 0; transform: translateY(10px); }
                    to { opacity: 1; transform: translateY(0); }
                }
                
                /* Responsive design */
                @media (max-width: 480px) {
                    .chatbot-panel {
                        width: calc(100vw - 40px);
                        right: -10px;
                        bottom: 80px;
                    }
                    
                    .chatbot-toggle {
                        width: 50px;
                        height: 50px;
                    }
                }
            </style>
        `;
        
        document.head.insertAdjacentHTML('beforeend', styles);
    }
    
    // Add event listeners
    function addEventListeners() {
        // Toggle button
        const toggleBtn = document.getElementById(`chatbot-toggle-${WIDGET_CONFIG.chatbotId}`);
        if (toggleBtn) {
            toggleBtn.addEventListener('click', toggleChatbot);
        }
        
        // Close button
        const closeBtn = document.getElementById(`chatbot-close-${WIDGET_CONFIG.chatbotId}`);
        if (closeBtn) {
            closeBtn.addEventListener('click', closeChatbot);
        }
        
        // Minimize button
        const minimizeBtn = document.getElementById(`chatbot-minimize-${WIDGET_CONFIG.chatbotId}`);
        if (minimizeBtn) {
            minimizeBtn.addEventListener('click', minimizeChatbot);
        }
        
        // Send button
        const sendBtn = document.getElementById(`chatbot-send-${WIDGET_CONFIG.chatbotId}`);
        if (sendBtn) {
            sendBtn.addEventListener('click', sendMessage);
        }
        
        // Input field
        const inputField = document.getElementById(`chatbot-input-${WIDGET_CONFIG.chatbotId}`);
        if (inputField) {
            inputField.addEventListener('input', handleInputChange);
            inputField.addEventListener('keydown', handleKeyDown);
        }
    }
    
    // Toggle chatbot
    function toggleChatbot() {
        if (WIDGET_CONFIG.isOpen) {
            closeChatbot();
        } else {
            openChatbot();
        }
    }
    
    // Open chatbot
    function openChatbot() {
        WIDGET_CONFIG.isOpen = true;
        const panel = document.getElementById(`chatbot-panel-${WIDGET_CONFIG.chatbotId}`);
        if (panel) {
            panel.classList.add('open');
        }
        
        // Focus input
        const input = document.getElementById(`chatbot-input-${WIDGET_CONFIG.chatbotId}`);
        if (input) {
            input.focus();
        }
        
        // Hide badge
        const badge = document.getElementById(`chatbot-badge-${WIDGET_CONFIG.chatbotId}`);
        if (badge) {
            badge.style.display = 'none';
        }
    }
    
    // Close chatbot
    function closeChatbot() {
        WIDGET_CONFIG.isOpen = false;
        const panel = document.getElementById(`chatbot-panel-${WIDGET_CONFIG.chatbotId}`);
        if (panel) {
            panel.classList.remove('open');
        }
    }
    
    // Minimize chatbot
    function minimizeChatbot() {
        if (WIDGET_CONFIG.isMinimized) {
            // Restore
            WIDGET_CONFIG.isMinimized = false;
            const panel = document.getElementById(`chatbot-panel-${WIDGET_CONFIG.chatbotId}`);
            if (panel) {
                panel.style.height = '500px';
            }
        } else {
            // Minimize
            WIDGET_CONFIG.isMinimized = true;
            const panel = document.getElementById(`chatbot-panel-${WIDGET_CONFIG.chatbotId}`);
            if (panel) {
                panel.style.height = '80px';
            }
        }
    }
    
    // Handle input change
    function handleInputChange() {
        const input = document.getElementById(`chatbot-input-${WIDGET_CONFIG.chatbotId}`);
        const sendBtn = document.getElementById(`chatbot-send-${WIDGET_CONFIG.chatbotId}`);
        
        if (input && sendBtn) {
            // Auto-resize textarea
            input.style.height = 'auto';
            input.style.height = Math.min(input.scrollHeight, 100) + 'px';
            
            // Enable/disable send button
            sendBtn.disabled = !input.value.trim();
        }
    }
    
    // Handle key down
    function handleKeyDown(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            sendMessage();
        }
    }
    
    // Send message
    async function sendMessage() {
        const input = document.getElementById(`chatbot-input-${WIDGET_CONFIG.chatbotId}`);
        if (!input || !input.value.trim()) return;
        
        const message = input.value.trim();
        input.value = '';
        input.style.height = 'auto';
        
        // Disable send button
        const sendBtn = document.getElementById(`chatbot-send-${WIDGET_CONFIG.chatbotId}`);
        if (sendBtn) {
            sendBtn.disabled = true;
        }
        
        // Add user message to chat
        addMessage(message, 'user');
        
        // Show typing indicator
        showTypingIndicator(true);
        
        try {
            // Send message to API
            const response = await sendMessageToAPI(message);
            
            if (response.success) {
                // Add AI response
                addMessage(response.response, 'assistant');
            } else {
                // Show error message
                addMessage('Sorry, I encountered an error. Please try again.', 'assistant');
            }
        } catch (error) {
            console.error('Error sending message:', error);
            addMessage('Sorry, I encountered an error. Please try again.', 'assistant');
        } finally {
            // Hide typing indicator
            showTypingIndicator(false);
        }
    }
    
    // Add message to chat
    function addMessage(content, role) {
        const messagesContainer = document.getElementById(`chatbot-messages-${WIDGET_CONFIG.chatbotId}`);
        if (!messagesContainer) return;
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `chatbot-message ${role}`;
        
        const avatar = document.createElement('div');
        avatar.className = 'chatbot-message-avatar';
        avatar.textContent = role === 'user' ? 'U' : 'A';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'chatbot-message-content';
        messageContent.textContent = content;
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        
        messagesContainer.appendChild(messageDiv);
        
        // Scroll to bottom
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        // Update unread count if closed
        if (!WIDGET_CONFIG.isOpen && role === 'assistant') {
            updateUnreadCount();
        }
    }
    
    // Show/hide typing indicator
    function showTypingIndicator(show) {
        const typingIndicator = document.getElementById(`chatbot-typing-${WIDGET_CONFIG.chatbotId}`);
        if (typingIndicator) {
            typingIndicator.style.display = show ? 'flex' : 'none';
        }
    }
    
    // Update unread count
    function updateUnreadCount() {
        const badge = document.getElementById(`chatbot-badge-${WIDGET_CONFIG.chatbotId}`);
        if (badge) {
            const currentCount = parseInt(badge.textContent) || 0;
            badge.textContent = currentCount + 1;
            badge.style.display = 'block';
        }
    }
    
    // Send message to API
    async function sendMessageToAPI(message) {
        if (!WIDGET_CONFIG.conversationId) {
            // Create new conversation
            await createConversation();
        }
        
        const response = await fetch(`${WIDGET_CONFIG.apiBaseUrl}/chatbot/message`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                conversation_id: WIDGET_CONFIG.conversationId,
                content: message,
                session_id: WIDGET_CONFIG.sessionId
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }
    
    // Create conversation
    async function createConversation() {
        try {
            const response = await fetch(`${WIDGET_CONFIG.apiBaseUrl}/chatbot/conversation`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    chatbot_id: WIDGET_CONFIG.chatbotId,
                    session_id: WIDGET_CONFIG.sessionId
                })
            });
            
            if (response.ok) {
                const data = await response.json();
                WIDGET_CONFIG.conversationId = data.id;
            }
        } catch (error) {
            console.error('Error creating conversation:', error);
        }
    }
    
    // Load chatbot configuration
    async function loadChatbotConfig() {
        try {
            const response = await fetch(`${WIDGET_CONFIG.apiBaseUrl}/chatbot/${WIDGET_CONFIG.chatbotId}`);
            if (response.ok) {
                const chatbot = await response.json();
                
                // Update UI with chatbot config
                updateChatbotUI(chatbot);
            }
        } catch (error) {
            console.error('Error loading chatbot config:', error);
        }
    }
    
    // Update chatbot UI
    function updateChatbotUI(chatbot) {
        // Update title
        const title = document.getElementById(`chatbot-title-${WIDGET_CONFIG.chatbotId}`);
        if (title && chatbot.config) {
            title.textContent = chatbot.config.name || 'Chat with us';
        }
        
        // Update subtitle
        const subtitle = document.getElementById(`chatbot-subtitle-${WIDGET_CONFIG.chatbotId}`);
        if (subtitle && chatbot.config) {
            subtitle.textContent = chatbot.config.description || 'We\'re here to help';
        }
        
        // Add greeting message if conversation is empty
        const messagesContainer = document.getElementById(`chatbot-messages-${WIDGET_CONFIG.chatbotId}`);
        if (messagesContainer && messagesContainer.children.length === 0 && chatbot.config) {
            addMessage(chatbot.config.greeting_message, 'assistant');
        }
    }
    
    // Initialize conversation
    function initConversation() {
        // Create initial conversation
        createConversation();
    }
    
    // Generate session ID
    function generateSessionId() {
        return 'sess_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
    }
    
    // Initialize widget when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initWidget);
    } else {
        initWidget();
    }
    
    // Expose public methods
    window.ChatbotWidget = {
        open: openChatbot,
        close: closeChatbot,
        sendMessage: sendMessage,
        getConfig: () => WIDGET_CONFIG
    };
    
})(); 