{"id": "conv_d5ece25fe473", "chatbot_id": "chatbot_bf0703644fe2", "user_id": null, "session_id": "simple_test_session", "started_at": "2025-08-24 13:13:19.696950", "last_activity": "2025-08-24 13:13:34.774035", "ended_at": null, "is_active": true, "total_messages": 8, "user_satisfaction": null, "resolution_time": null, "lead_qualified": true, "lead_score": 0.5, "human_handoff_occurred": false, "messages": [{"id": "msg_76b4cce1fe5b", "conversation_id": "conv_d5ece25fe473", "content": "What services do you offer?", "role": "user", "message_type": "text", "timestamp": "2025-08-24 13:13:21.745241", "processing_time": 6.366379261016846, "detected_intent": "product_inquiry", "confidence_score": 0.5, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": false, "lead_score": 0.0, "requires_human_handoff": false}, {"id": "msg_047868926357", "conversation_id": "conv_d5ece25fe473", "content": "TechCorp Solutions offers three main services:\n\n🤖 **AI Chatbot Development** - Create intelligent chatbots with advanced RAG capabilities, multi-model support, and tool calling features.\n\n💻 **Custom Software Development** - Full-stack web applications, mobile apps, and enterprise software solutions using modern technologies.\n\n🎓 **AI Consulting & Training** - Strategic AI implementation guidance, team training, and ongoing support.\n\nWould you like to know more about any of these services?", "role": "assistant", "message_type": "text", "timestamp": "2025-08-24 13:13:28.110716", "processing_time": null, "detected_intent": null, "confidence_score": null, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": null, "lead_score": null, "requires_human_handoff": false}, {"id": "msg_872e36d1f85c", "conversation_id": "conv_d5ece25fe473", "content": "How much does the Professional plan cost?", "role": "user", "message_type": "text", "timestamp": "2025-08-24 13:13:30.172191", "processing_time": 0.1759507656097412, "detected_intent": "pricing_inquiry", "confidence_score": 0.9, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": true, "lead_score": 0.5, "requires_human_handoff": false}, {"id": "msg_8fede53cd574", "conversation_id": "conv_d5ece25fe473", "content": "Our pricing plans are:\n\n💰 **Starter Plan - $2,999** - Basic chatbot setup with single model integration\n🚀 **Professional Plan - $7,999** - Advanced AI chatbot with multi-model support\n🏢 **Enterprise Plan - $19,999** - Custom AI solutions with full integration\n\nEach plan includes different levels of support and features. Would you like details about a specific plan?", "role": "assistant", "message_type": "text", "timestamp": "2025-08-24 13:13:30.347224", "processing_time": null, "detected_intent": null, "confidence_score": null, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": null, "lead_score": null, "requires_human_handoff": false}, {"id": "msg_ed34441db977", "conversation_id": "conv_d5ece25fe473", "content": "What are your business hours?", "role": "user", "message_type": "text", "timestamp": "2025-08-24 13:13:32.396918", "processing_time": 0.1589052677154541, "detected_intent": "unknown", "confidence_score": 0.3, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": false, "lead_score": 0.0, "requires_human_handoff": false}, {"id": "msg_741503c392f4", "conversation_id": "conv_d5ece25fe473", "content": "TechCorp Solutions offers three main services:\n\n🤖 **AI Chatbot Development** - Create intelligent chatbots with advanced RAG capabilities, multi-model support, and tool calling features.\n\n💻 **Custom Software Development** - Full-stack web applications, mobile apps, and enterprise software solutions using modern technologies.\n\n🎓 **AI Consulting & Training** - Strategic AI implementation guidance, team training, and ongoing support.\n\nWould you like to know more about any of these services?", "role": "assistant", "message_type": "text", "timestamp": "2025-08-24 13:13:32.554840", "processing_time": null, "detected_intent": null, "confidence_score": null, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": null, "lead_score": null, "requires_human_handoff": false}, {"id": "msg_cee9a7285453", "conversation_id": "conv_d5ece25fe473", "content": "How can I contact you?", "role": "user", "message_type": "text", "timestamp": "2025-08-24 13:13:34.620141", "processing_time": 0.15495753288269043, "detected_intent": "lead_qualification", "confidence_score": 0.5, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": true, "lead_score": 0.5, "requires_human_handoff": false}, {"id": "msg_853e57bd0105", "conversation_id": "conv_d5ece25fe473", "content": "You can reach us at:\n\n📧 **Email**: <EMAIL>\n📞 **Phone**: +****************\n🕒 **Hours**: Monday-Friday, 9:00 AM - 6:00 PM EST\n\nWe'd be happy to discuss your specific needs!", "role": "assistant", "message_type": "text", "timestamp": "2025-08-24 13:13:34.773997", "processing_time": null, "detected_intent": null, "confidence_score": null, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": null, "lead_score": null, "requires_human_handoff": false}]}