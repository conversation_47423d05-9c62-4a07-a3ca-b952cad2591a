{"id": "conv_20f94e42498a", "chatbot_id": "chatbot_5fd0cd786ade", "user_id": null, "session_id": "simple_test_session", "started_at": "2025-08-24 13:04:52.423665", "last_activity": "2025-08-24 13:05:06.920419", "ended_at": null, "is_active": true, "total_messages": 8, "user_satisfaction": null, "resolution_time": null, "lead_qualified": true, "lead_score": 0.5, "human_handoff_occurred": false, "messages": [{"id": "msg_00ed9076df32", "conversation_id": "conv_20f94e42498a", "content": "What services do you offer?", "role": "user", "message_type": "text", "timestamp": "2025-08-24 13:04:54.485445", "processing_time": 5.39307713508606, "detected_intent": "product_inquiry", "confidence_score": 0.5, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": false, "lead_score": 0.0, "requires_human_handoff": false}, {"id": "msg_ee4a016ca7e4", "conversation_id": "conv_20f94e42498a", "content": "I'm sorry, I don't have information about that. Please try rephrasing your question.", "role": "assistant", "message_type": "text", "timestamp": "2025-08-24 13:04:59.877136", "processing_time": null, "detected_intent": null, "confidence_score": null, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": null, "lead_score": null, "requires_human_handoff": false}, {"id": "msg_bf4cd9a26c9c", "conversation_id": "conv_20f94e42498a", "content": "How much does the Professional plan cost?", "role": "user", "message_type": "text", "timestamp": "2025-08-24 13:05:01.936057", "processing_time": 0.31008052825927734, "detected_intent": "pricing_inquiry", "confidence_score": 0.9, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": true, "lead_score": 0.5, "requires_human_handoff": false}, {"id": "msg_32b1c3f2be14", "conversation_id": "conv_20f94e42498a", "content": "I'm sorry, I don't have information about that. Please try rephrasing your question.", "role": "assistant", "message_type": "text", "timestamp": "2025-08-24 13:05:02.244347", "processing_time": null, "detected_intent": null, "confidence_score": null, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": null, "lead_score": null, "requires_human_handoff": false}, {"id": "msg_44bc0698741e", "conversation_id": "conv_20f94e42498a", "content": "What are your business hours?", "role": "user", "message_type": "text", "timestamp": "2025-08-24 13:05:04.302937", "processing_time": 0.2997112274169922, "detected_intent": "unknown", "confidence_score": 0.3, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": false, "lead_score": 0.0, "requires_human_handoff": false}, {"id": "msg_677a3bf0b533", "conversation_id": "conv_20f94e42498a", "content": "I'm sorry, I don't have information about that. Please try rephrasing your question.", "role": "assistant", "message_type": "text", "timestamp": "2025-08-24 13:05:04.600829", "processing_time": null, "detected_intent": null, "confidence_score": null, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": null, "lead_score": null, "requires_human_handoff": false}, {"id": "msg_ef03c517e567", "conversation_id": "conv_20f94e42498a", "content": "How can I contact you?", "role": "user", "message_type": "text", "timestamp": "2025-08-24 13:05:06.647030", "processing_time": 0.27510833740234375, "detected_intent": "lead_qualification", "confidence_score": 0.5, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": true, "lead_score": 0.5, "requires_human_handoff": false}, {"id": "msg_84ed29632d8d", "conversation_id": "conv_20f94e42498a", "content": "I'm sorry, I don't have information about that. Please try rephrasing your question.", "role": "assistant", "message_type": "text", "timestamp": "2025-08-24 13:05:06.920369", "processing_time": null, "detected_intent": null, "confidence_score": null, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": null, "lead_score": null, "requires_human_handoff": false}]}