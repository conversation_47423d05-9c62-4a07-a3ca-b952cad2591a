{"id": "conv_dfe1d39b373c", "chatbot_id": "chatbot_bcd39fb986ed", "user_id": null, "session_id": "simple_test_session", "started_at": "2025-08-24 13:02:28.380433", "last_activity": "2025-08-24 13:02:43.832456", "ended_at": null, "is_active": true, "total_messages": 8, "user_satisfaction": null, "resolution_time": null, "lead_qualified": true, "lead_score": 0.5, "human_handoff_occurred": false, "messages": [{"id": "msg_b272c0256b78", "conversation_id": "conv_dfe1d39b373c", "content": "What services do you offer?", "role": "user", "message_type": "text", "timestamp": "2025-08-24 13:02:30.430291", "processing_time": 6.762200832366943, "detected_intent": "product_inquiry", "confidence_score": 0.5, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": false, "lead_score": 0.0, "requires_human_handoff": false}, {"id": "msg_6d69421e9f11", "conversation_id": "conv_dfe1d39b373c", "content": "I'm sorry, I don't have information about that. Please try rephrasing your question.", "role": "assistant", "message_type": "text", "timestamp": "2025-08-24 13:02:37.191648", "processing_time": null, "detected_intent": null, "confidence_score": null, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": null, "lead_score": null, "requires_human_handoff": false}, {"id": "msg_4d9f900869b7", "conversation_id": "conv_dfe1d39b373c", "content": "How much does the Professional plan cost?", "role": "user", "message_type": "text", "timestamp": "2025-08-24 13:02:39.245892", "processing_time": 0.1680586338043213, "detected_intent": "pricing_inquiry", "confidence_score": 0.9, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": true, "lead_score": 0.5, "requires_human_handoff": false}, {"id": "msg_fdf6395659d3", "conversation_id": "conv_dfe1d39b373c", "content": "I'm sorry, I don't have information about that. Please try rephrasing your question.", "role": "assistant", "message_type": "text", "timestamp": "2025-08-24 13:02:39.413071", "processing_time": null, "detected_intent": null, "confidence_score": null, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": null, "lead_score": null, "requires_human_handoff": false}, {"id": "msg_61d358646970", "conversation_id": "conv_dfe1d39b373c", "content": "What are your business hours?", "role": "user", "message_type": "text", "timestamp": "2025-08-24 13:02:41.478073", "processing_time": 0.1546154022216797, "detected_intent": "unknown", "confidence_score": 0.3, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": false, "lead_score": 0.0, "requires_human_handoff": false}, {"id": "msg_646b1d0092f7", "conversation_id": "conv_dfe1d39b373c", "content": "I'm sorry, I don't have information about that. Please try rephrasing your question.", "role": "assistant", "message_type": "text", "timestamp": "2025-08-24 13:02:41.631768", "processing_time": null, "detected_intent": null, "confidence_score": null, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": null, "lead_score": null, "requires_human_handoff": false}, {"id": "msg_f5e1a31483e1", "conversation_id": "conv_dfe1d39b373c", "content": "How can I contact you?", "role": "user", "message_type": "text", "timestamp": "2025-08-24 13:02:43.679644", "processing_time": 0.15370535850524902, "detected_intent": "lead_qualification", "confidence_score": 0.5, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": true, "lead_score": 0.5, "requires_human_handoff": false}, {"id": "msg_8d7d80bd5bbd", "conversation_id": "conv_dfe1d39b373c", "content": "I'm sorry, I don't have information about that. Please try rephrasing your question.", "role": "assistant", "message_type": "text", "timestamp": "2025-08-24 13:02:43.832422", "processing_time": null, "detected_intent": null, "confidence_score": null, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": null, "lead_score": null, "requires_human_handoff": false}]}