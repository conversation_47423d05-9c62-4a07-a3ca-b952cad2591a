"""
Tool Calling Service for Function-Based AI Interactions
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser

from services.ai_model_service import AIModelService, ModelCapability
from utils.config import settings
from utils.logger import get_logger

logger = get_logger(__name__)

class ToolType(str, Enum):
    """Available tool types"""
    SEARCH = "search"
    CALCULATION = "calculation"
    DATETIME = "datetime"
    WEATHER = "weather"
    EMAIL = "email"
    CALENDAR = "calendar"
    DATABASE = "database"
    API_CALL = "api_call"
    FILE_OPERATION = "file_operation"

@dataclass
class ToolDefinition:
    """Definition of a callable tool"""
    name: str
    description: str
    tool_type: ToolType
    parameters: Dict[str, Any]
    function: Callable
    requires_auth: bool = False
    rate_limit: Optional[int] = None  # calls per minute

@dataclass
class ToolCall:
    """Represents a tool call request"""
    tool_name: str
    parameters: Dict[str, Any]
    timestamp: datetime
    user_id: Optional[str] = None

@dataclass
class ToolResult:
    """Result of a tool call"""
    success: bool
    result: Any
    error_message: Optional[str] = None
    execution_time: float = 0.0

class ToolCallingService:
    """Service for managing and executing tool calls"""
    
    def __init__(self):
        """Initialize the tool calling service"""
        self.ai_model_service = AIModelService()
        self._tools: Dict[str, ToolDefinition] = {}
        self._call_history: List[ToolCall] = []
        self._rate_limits: Dict[str, List[datetime]] = {}
        
        # Register built-in tools
        self._register_builtin_tools()
    
    def _register_builtin_tools(self):
        """Register built-in tools"""
        
        # Search tool
        self.register_tool(ToolDefinition(
            name="search_knowledge_base",
            description="Search the knowledge base for information",
            tool_type=ToolType.SEARCH,
            parameters={
                "query": {"type": "string", "description": "Search query"},
                "limit": {"type": "integer", "description": "Maximum number of results", "default": 5}
            },
            function=self._search_knowledge_base
        ))
        
        # Calculator tool
        self.register_tool(ToolDefinition(
            name="calculate",
            description="Perform mathematical calculations",
            tool_type=ToolType.CALCULATION,
            parameters={
                "expression": {"type": "string", "description": "Mathematical expression to evaluate"}
            },
            function=self._calculate
        ))
        
        # DateTime tool
        self.register_tool(ToolDefinition(
            name="get_current_time",
            description="Get current date and time",
            tool_type=ToolType.DATETIME,
            parameters={
                "timezone": {"type": "string", "description": "Timezone (optional)", "default": "UTC"}
            },
            function=self._get_current_time
        ))
        
        # Business hours checker
        self.register_tool(ToolDefinition(
            name="check_business_hours",
            description="Check if current time is within business hours",
            tool_type=ToolType.DATETIME,
            parameters={
                "timezone": {"type": "string", "description": "Business timezone", "default": "UTC"}
            },
            function=self._check_business_hours
        ))
        
        # Email validator
        self.register_tool(ToolDefinition(
            name="validate_email",
            description="Validate email address format",
            tool_type=ToolType.EMAIL,
            parameters={
                "email": {"type": "string", "description": "Email address to validate"}
            },
            function=self._validate_email
        ))
    
    def register_tool(self, tool: ToolDefinition):
        """Register a new tool"""
        self._tools[tool.name] = tool
        logger.info(f"Registered tool: {tool.name}")
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get list of available tools"""
        return [
            {
                "name": tool.name,
                "description": tool.description,
                "type": tool.tool_type.value,
                "parameters": tool.parameters,
                "requires_auth": tool.requires_auth
            }
            for tool in self._tools.values()
        ]
    
    async def detect_tool_calls(self, query: str, chatbot_id: str) -> List[ToolCall]:
        """
        Detect if a query requires tool calls and extract the calls
        
        Args:
            query: User query
            chatbot_id: Chatbot identifier
            
        Returns:
            List of detected tool calls
        """
        try:
            # Select model with tool calling capability
            model_id = self.ai_model_service.select_best_model(
                query,
                required_capabilities=[ModelCapability.TOOL_CALLING]
            )
            
            llm = self.ai_model_service.get_llm_instance(model_id, temperature=0.1)
            
            # Create tool detection prompt
            tools_description = self._format_tools_for_prompt()
            
            prompt = ChatPromptTemplate.from_template("""
            You are an AI assistant that can detect when a user query requires tool calls.
            
            Available tools:
            {tools}
            
            User query: {query}
            
            Analyze the query and determine if any tools should be called. If tools are needed, respond with a JSON array of tool calls in this format:
            [
                {{
                    "tool_name": "tool_name",
                    "parameters": {{"param1": "value1", "param2": "value2"}}
                }}
            ]
            
            If no tools are needed, respond with an empty array: []
            
            Tool calls:
            """)
            
            chain = prompt | llm | StrOutputParser()
            response = await asyncio.to_thread(chain.invoke, {
                "tools": tools_description,
                "query": query
            })
            
            # Parse the response
            tool_calls = self._parse_tool_calls_response(response)
            return tool_calls
            
        except Exception as e:
            logger.error(f"Error detecting tool calls: {e}")
            return []
    
    def _format_tools_for_prompt(self) -> str:
        """Format tools for inclusion in prompts"""
        tools_text = []
        
        for tool in self._tools.values():
            params_text = []
            for param_name, param_info in tool.parameters.items():
                param_desc = f"{param_name} ({param_info['type']}): {param_info['description']}"
                if 'default' in param_info:
                    param_desc += f" (default: {param_info['default']})"
                params_text.append(param_desc)
            
            tool_text = f"- {tool.name}: {tool.description}\n  Parameters: {', '.join(params_text)}"
            tools_text.append(tool_text)
        
        return "\n".join(tools_text)
    
    def _parse_tool_calls_response(self, response: str) -> List[ToolCall]:
        """Parse tool calls from LLM response"""
        try:
            # Extract JSON from response
            json_start = response.find('[')
            json_end = response.rfind(']') + 1
            
            if json_start == -1 or json_end == 0:
                return []
            
            json_str = response[json_start:json_end]
            tool_calls_data = json.loads(json_str)
            
            tool_calls = []
            for call_data in tool_calls_data:
                if 'tool_name' in call_data and 'parameters' in call_data:
                    tool_calls.append(ToolCall(
                        tool_name=call_data['tool_name'],
                        parameters=call_data['parameters'],
                        timestamp=datetime.utcnow()
                    ))
            
            return tool_calls
            
        except Exception as e:
            logger.error(f"Error parsing tool calls response: {e}")
            return []
    
    async def execute_tool_call(self, tool_call: ToolCall) -> ToolResult:
        """Execute a tool call"""
        start_time = datetime.utcnow()
        
        try:
            # Check if tool exists
            if tool_call.tool_name not in self._tools:
                return ToolResult(
                    success=False,
                    result=None,
                    error_message=f"Tool '{tool_call.tool_name}' not found"
                )
            
            tool = self._tools[tool_call.tool_name]
            
            # Check rate limits
            if not self._check_rate_limit(tool_call.tool_name, tool.rate_limit):
                return ToolResult(
                    success=False,
                    result=None,
                    error_message=f"Rate limit exceeded for tool '{tool_call.tool_name}'"
                )
            
            # Validate parameters
            validation_error = self._validate_parameters(tool_call.parameters, tool.parameters)
            if validation_error:
                return ToolResult(
                    success=False,
                    result=None,
                    error_message=validation_error
                )
            
            # Execute the tool
            result = await asyncio.to_thread(tool.function, **tool_call.parameters)
            
            # Record the call
            self._call_history.append(tool_call)
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            return ToolResult(
                success=True,
                result=result,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            logger.error(f"Error executing tool call {tool_call.tool_name}: {e}")
            
            return ToolResult(
                success=False,
                result=None,
                error_message=str(e),
                execution_time=execution_time
            )
    
    def _check_rate_limit(self, tool_name: str, rate_limit: Optional[int]) -> bool:
        """Check if tool call is within rate limits"""
        if rate_limit is None:
            return True
        
        now = datetime.utcnow()
        minute_ago = now - timedelta(minutes=1)
        
        # Clean old entries
        if tool_name in self._rate_limits:
            self._rate_limits[tool_name] = [
                timestamp for timestamp in self._rate_limits[tool_name]
                if timestamp > minute_ago
            ]
        else:
            self._rate_limits[tool_name] = []
        
        # Check limit
        if len(self._rate_limits[tool_name]) >= rate_limit:
            return False
        
        # Record this call
        self._rate_limits[tool_name].append(now)
        return True
    
    def _validate_parameters(self, provided_params: Dict[str, Any], expected_params: Dict[str, Any]) -> Optional[str]:
        """Validate tool parameters"""
        for param_name, param_info in expected_params.items():
            if param_name not in provided_params:
                if 'default' not in param_info:
                    return f"Missing required parameter: {param_name}"
                else:
                    provided_params[param_name] = param_info['default']
        
        return None
    
    # Built-in tool implementations
    
    def _search_knowledge_base(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """Search the knowledge base"""
        # This would integrate with the vector store service
        return {
            "query": query,
            "results": f"Found {limit} results for '{query}'",
            "count": limit
        }
    
    def _calculate(self, expression: str) -> Dict[str, Any]:
        """Perform mathematical calculation"""
        try:
            # Simple calculator - in production, use a safer evaluation method
            import ast
            import operator
            
            # Supported operations
            ops = {
                ast.Add: operator.add,
                ast.Sub: operator.sub,
                ast.Mult: operator.mul,
                ast.Div: operator.truediv,
                ast.Pow: operator.pow,
                ast.USub: operator.neg,
            }
            
            def eval_expr(node):
                if isinstance(node, ast.Num):
                    return node.n
                elif isinstance(node, ast.BinOp):
                    return ops[type(node.op)](eval_expr(node.left), eval_expr(node.right))
                elif isinstance(node, ast.UnaryOp):
                    return ops[type(node.op)](eval_expr(node.operand))
                else:
                    raise TypeError(node)
            
            result = eval_expr(ast.parse(expression, mode='eval').body)
            
            return {
                "expression": expression,
                "result": result,
                "success": True
            }
            
        except Exception as e:
            return {
                "expression": expression,
                "error": str(e),
                "success": False
            }
    
    def _get_current_time(self, timezone: str = "UTC") -> Dict[str, Any]:
        """Get current date and time"""
        now = datetime.utcnow()
        
        return {
            "current_time": now.isoformat(),
            "timezone": timezone,
            "formatted": now.strftime("%Y-%m-%d %H:%M:%S UTC"),
            "timestamp": now.timestamp()
        }
    
    def _check_business_hours(self, timezone: str = "UTC") -> Dict[str, Any]:
        """Check if current time is within business hours"""
        now = datetime.utcnow()
        
        # Simple business hours check (9 AM - 5 PM, Monday-Friday)
        weekday = now.weekday()  # 0 = Monday, 6 = Sunday
        hour = now.hour
        
        is_business_day = weekday < 5  # Monday to Friday
        is_business_hour = 9 <= hour < 17  # 9 AM to 5 PM
        is_business_time = is_business_day and is_business_hour
        
        return {
            "is_business_hours": is_business_time,
            "current_time": now.strftime("%Y-%m-%d %H:%M:%S UTC"),
            "business_hours": "Monday-Friday, 9:00 AM - 5:00 PM UTC",
            "next_business_day": self._get_next_business_day(now).strftime("%Y-%m-%d")
        }
    
    def _get_next_business_day(self, current_date: datetime) -> datetime:
        """Get the next business day"""
        next_day = current_date + timedelta(days=1)
        
        while next_day.weekday() >= 5:  # Skip weekends
            next_day += timedelta(days=1)
        
        return next_day
    
    def _validate_email(self, email: str) -> Dict[str, Any]:
        """Validate email address format"""
        import re
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        is_valid = bool(re.match(email_pattern, email))
        
        return {
            "email": email,
            "is_valid": is_valid,
            "format_check": "passed" if is_valid else "failed"
        }
