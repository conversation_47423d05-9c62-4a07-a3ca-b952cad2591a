{"id": "chatbot_ca4e7d3f6606", "website_url": "https://nodevbuild.com/", "company_name": "nodevbuild", "config": {"name": "Test Assistant", "description": "Test chatbot created via dashboard", "primary_color": "#667eea", "secondary_color": "#764ba2", "logo_url": null, "personality": "friendly", "greeting_message": "Hello! I'm your test assistant. How can I help you today?", "fallback_message": "I'm sorry, I don't have information about that. Please try rephrasing your question.", "max_context_length": 8192, "temperature": 0.1, "max_response_length": 500, "custom_knowledge": null, "include_website_content": true, "include_uploaded_documents": true, "business_hours": null, "lead_qualification_enabled": true, "human_handoff_enabled": true}, "status": "active", "created_at": "2025-08-19 17:50:01.791726", "updated_at": "2025-08-19 17:50:06.456315", "vectorstore_path": "qdrant_db/websites/chatbot_ca4e7d3f6606", "knowledge_base_size": 1, "last_training": "2025-08-19 17:50:06.456292", "total_conversations": 0, "total_messages": 0, "average_response_time": 0.0, "embed_code": "<!-- Website Chatbot for https://nodevbuild.com/ -->\n<div id=\"chatbot-widget-chatbot_ca4e7d3f6606\" class=\"chatbot-widget\"></div>\n<script>\n(function() {\n    var script = document.createElement('script');\n    script.src = 'https://yourdomain.com/widget.js?id=chatbot_ca4e7d3f6606';\n    script.async = true;\n    document.head.appendChild(script);\n})();\n</script>"}