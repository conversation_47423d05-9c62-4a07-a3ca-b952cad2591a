{"id": "chatbot_00068e09cd5b", "website_url": "http://localhost:8000/test_dashboard.html", "company_name": "Test Company", "config": {"name": "Test Assistant", "description": "Test chatbot created via dashboard", "primary_color": "#667eea", "secondary_color": "#764ba2", "logo_url": null, "personality": "friendly", "greeting_message": "Hello! I'm your test assistant. How can I help you today?", "fallback_message": "I'm sorry, I don't have information about that. Please try rephrasing your question.", "max_context_length": 8192, "temperature": 0.1, "max_response_length": 500, "custom_knowledge": null, "include_website_content": true, "include_uploaded_documents": true, "business_hours": null, "lead_qualification_enabled": true, "human_handoff_enabled": true}, "status": "error", "created_at": "2025-08-17 13:18:39.445635", "updated_at": "2025-08-17 13:18:40.460270", "vectorstore_path": "qdrant_db/websites/chatbot_00068e09cd5b", "knowledge_base_size": 0, "last_training": null, "total_conversations": 0, "total_messages": 0, "average_response_time": 0.0, "embed_code": "<!-- Website Chatbot for http://localhost:8000/test_dashboard.html -->\n<div id=\"chatbot-widget-chatbot_00068e09cd5b\" class=\"chatbot-widget\"></div>\n<script>\n(function() {\n    var script = document.createElement('script');\n    script.src = 'https://yourdomain.com/widget.js?id=chatbot_00068e09cd5b';\n    script.async = true;\n    document.head.appendChild(script);\n})();\n</script>"}