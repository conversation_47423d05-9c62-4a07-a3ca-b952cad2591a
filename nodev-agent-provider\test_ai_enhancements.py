#!/usr/bin/env python3
"""
Test script for AI & Model Enhancements
"""

import asyncio
import json
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_ai_model_service():
    """Test AI Model Service functionality"""
    print("\n🧠 Testing AI Model Service...")
    
    try:
        from services.ai_model_service import AIModelService, ModelCapability, ModelType
        
        ai_service = AIModelService()
        
        # Test 1: Get available models
        print("📋 Available models:")
        models = ai_service.get_available_models()
        for model in models:
            print(f"  - {model['name']} ({model['model_id']})")
            print(f"    Type: {model['type']}, Capabilities: {model['capabilities']}")
            print(f"    Quality: {model['quality_score']:.2f}")
        
        # Test 2: Model selection
        test_queries = [
            "How do I write a Python function?",
            "What is 2 + 2 * 3?",
            "Tell me a creative story about AI",
            "Analyze the performance data from last quarter"
        ]
        
        print("\n🎯 Model selection tests:")
        for query in test_queries:
            selected = ai_service.select_best_model(query)
            print(f"  Query: '{query}'")
            print(f"  Selected: {selected}")
        
        # Test 3: Model with specific capabilities
        selected_tool_model = ai_service.select_best_model(
            "Calculate the area of a circle with radius 5",
            required_capabilities=[ModelCapability.TOOL_CALLING]
        )
        print(f"\n🛠️  Tool calling model: {selected_tool_model}")
        
        print("✅ AI Model Service tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ AI Model Service test failed: {e}")
        return False

async def test_advanced_rag_service():
    """Test Advanced RAG Service functionality"""
    print("\n🔍 Testing Advanced RAG Service...")
    
    try:
        from services.advanced_rag_service import AdvancedRAGService
        
        rag_service = AdvancedRAGService()
        
        # Test query analysis
        test_query = "How do I integrate payment processing with my e-commerce website?"
        
        print(f"📝 Analyzing query: '{test_query}'")
        analysis = await rag_service._analyze_query(test_query)
        
        print(f"  Intent: {analysis.intent}")
        print(f"  Complexity: {analysis.complexity}")
        print(f"  Keywords: {analysis.keywords}")
        print(f"  Multi-hop: {analysis.requires_multi_hop}")
        print(f"  Expanded queries: {len(analysis.expanded_queries)}")
        
        for i, expanded in enumerate(analysis.expanded_queries, 1):
            print(f"    {i}. {expanded}")
        
        print("✅ Advanced RAG Service tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Advanced RAG Service test failed: {e}")
        return False

async def test_tool_calling_service():
    """Test Tool Calling Service functionality"""
    print("\n🛠️  Testing Tool Calling Service...")
    
    try:
        from services.tool_calling_service import ToolCallingService
        
        tool_service = ToolCallingService()
        
        # Test 1: Get available tools
        print("🔧 Available tools:")
        tools = tool_service.get_available_tools()
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description']}")
            print(f"    Type: {tool['type']}, Auth required: {tool['requires_auth']}")
        
        # Test 2: Tool call detection
        test_queries = [
            "What time is it?",
            "Calculate 15 * 24 + 100",
            "Is my <NAME_EMAIL> valid?",
            "Are we currently in business hours?",
            "Just tell me about your services"
        ]
        
        print("\n🎯 Tool call detection tests:")
        for query in test_queries:
            tool_calls = await tool_service.detect_tool_calls(query, "test_chatbot")
            print(f"  Query: '{query}'")
            if tool_calls:
                for tc in tool_calls:
                    print(f"    Tool: {tc.tool_name}, Params: {tc.parameters}")
            else:
                print("    No tools needed")
        
        # Test 3: Execute a tool call
        from services.tool_calling_service import ToolCall
        
        print("\n⚡ Executing tool calls:")
        
        # Test calculator
        calc_call = ToolCall(
            tool_name="calculate",
            parameters={"expression": "2 + 3 * 4"},
            timestamp=datetime.utcnow()
        )
        
        result = await tool_service.execute_tool_call(calc_call)
        print(f"  Calculator: {calc_call.parameters['expression']} = {result.result}")
        
        # Test email validation
        email_call = ToolCall(
            tool_name="validate_email",
            parameters={"email": "<EMAIL>"},
            timestamp=datetime.utcnow()
        )
        
        result = await tool_service.execute_tool_call(email_call)
        print(f"  Email validation: {result.result}")
        
        # Test current time
        time_call = ToolCall(
            tool_name="get_current_time",
            parameters={},
            timestamp=datetime.utcnow()
        )
        
        result = await tool_service.execute_tool_call(time_call)
        print(f"  Current time: {result.result['formatted']}")
        
        print("✅ Tool Calling Service tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Tool Calling Service test failed: {e}")
        return False

async def test_integration():
    """Test integration between services"""
    print("\n🔗 Testing Service Integration...")
    
    try:
        # This would test the enhanced chatbot service
        # For now, just verify imports work
        from services.chatbot_service import ChatbotService
        
        chatbot_service = ChatbotService()
        print("✅ Enhanced ChatbotService initialized successfully!")
        
        # Test that all enhanced services are accessible
        assert hasattr(chatbot_service, 'ai_model_service')
        assert hasattr(chatbot_service, 'advanced_rag_service')
        assert hasattr(chatbot_service, 'tool_calling_service')
        
        print("✅ All enhanced services are properly integrated!")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Starting AI & Model Enhancements Tests")
    print("=" * 50)
    
    tests = [
        ("AI Model Service", test_ai_model_service),
        ("Advanced RAG Service", test_advanced_rag_service),
        ("Tool Calling Service", test_tool_calling_service),
        ("Service Integration", test_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All AI enhancements are working correctly!")
    else:
        print("⚠️  Some tests failed. Check the logs above for details.")
    
    return passed == len(results)

if __name__ == "__main__":
    asyncio.run(main())
