{"id": "chatbot_637be9092d3b", "website_url": "http://localhost:8080/test_page.html", "company_name": "Demo Company", "config": {"name": "Demo Assistant", "description": "AI assistant for demo purposes", "primary_color": "#667eea", "secondary_color": "#764ba2", "logo_url": null, "personality": "friendly", "greeting_message": "Hello! I am your demo assistant. How can I help you today?", "fallback_message": "I'm sorry, I don't have information about that. Please try rephrasing your question.", "max_context_length": 8192, "temperature": 0.1, "max_response_length": 500, "custom_knowledge": null, "include_website_content": true, "include_uploaded_documents": true, "business_hours": null, "lead_qualification_enabled": true, "human_handoff_enabled": true}, "status": "error", "created_at": "2025-08-17 12:39:57.697937", "updated_at": "2025-08-17 12:39:58.707547", "vectorstore_path": "qdrant_db/websites/chatbot_637be9092d3b", "knowledge_base_size": 0, "last_training": null, "total_conversations": 0, "total_messages": 0, "average_response_time": 0.0, "embed_code": "<!-- Website Chatbot for http://localhost:8080/test_page.html -->\n<div id=\"chatbot-widget-chatbot_637be9092d3b\" class=\"chatbot-widget\"></div>\n<script>\n(function() {\n    var script = document.createElement('script');\n    script.src = 'https://yourdomain.com/widget.js?id=chatbot_637be9092d3b';\n    script.async = true;\n    document.head.appendChild(script);\n})();\n</script>"}