"""
Advanced AI Model Service with Multi-Model Support and Intelligent Routing
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

from langchain_ollama import ChatOllama
from langchain_core.prompts import Chat<PERSON><PERSON>pt<PERSON>emplate
from langchain_core.runnables import RunnablePassthrough
from langchain_core.output_parsers import StrOutputParser
from langchain_core.documents import Document

from utils.config import settings
from utils.logger import get_logger

logger = get_logger(__name__)

class ModelType(str, Enum):
    """Available model types"""
    GENERAL = "general"
    TECHNICAL = "technical"
    CREATIVE = "creative"
    ANALYTICAL = "analytical"
    CONVERSATIONAL = "conversational"

class ModelCapability(str, Enum):
    """Model capabilities"""
    TOOL_CALLING = "tool_calling"
    CODE_GENERATION = "code_generation"
    REASONING = "reasoning"
    MULTILINGUAL = "multilingual"
    LONG_CONTEXT = "long_context"

@dataclass
class ModelConfig:
    """Configuration for a specific model"""
    name: str
    model_id: str
    model_type: ModelType
    capabilities: List[ModelCapability]
    max_context: int
    temperature_range: tuple
    cost_per_token: float = 0.0  # For future cost optimization
    response_time_avg: float = 1.0  # Average response time in seconds
    quality_score: float = 0.8  # Quality score (0-1)

class AIModelService:
    """Advanced AI Model Service with intelligent routing and multi-model support"""
    
    def __init__(self):
        """Initialize the AI model service"""
        self._models: Dict[str, ModelConfig] = {}
        self._llm_instances: Dict[str, ChatOllama] = {}
        self._model_performance: Dict[str, Dict[str, float]] = {}
        
        # Initialize available models
        self._initialize_models()
        
    def _initialize_models(self):
        """Initialize available models configuration"""
        
        # Define available models with their capabilities
        models = [
            ModelConfig(
                name="Gemma 3 Latest",
                model_id="gemma3:latest",
                model_type=ModelType.GENERAL,
                capabilities=[ModelCapability.REASONING, ModelCapability.CONVERSATIONAL],
                max_context=8192,
                temperature_range=(0.0, 1.0),
                quality_score=0.85,
                response_time_avg=1.2
            ),
            ModelConfig(
                name="Llama 3.1 8B",
                model_id="llama3.1:8b",
                model_type=ModelType.GENERAL,
                capabilities=[ModelCapability.TOOL_CALLING, ModelCapability.REASONING, ModelCapability.MULTILINGUAL],
                max_context=32768,
                temperature_range=(0.0, 1.0),
                quality_score=0.9,
                response_time_avg=1.5
            ),
            ModelConfig(
                name="Mistral 7B",
                model_id="mistral:7b",
                model_type=ModelType.TECHNICAL,
                capabilities=[ModelCapability.TOOL_CALLING, ModelCapability.CODE_GENERATION, ModelCapability.REASONING],
                max_context=8192,
                temperature_range=(0.0, 1.0),
                quality_score=0.88,
                response_time_avg=1.1
            ),
            ModelConfig(
                name="CodeLlama 7B",
                model_id="codellama:7b",
                model_type=ModelType.TECHNICAL,
                capabilities=[ModelCapability.CODE_GENERATION, ModelCapability.REASONING],
                max_context=16384,
                temperature_range=(0.0, 0.8),
                quality_score=0.92,
                response_time_avg=1.3
            ),
            ModelConfig(
                name="Qwen 2.5 7B",
                model_id="qwen2.5:7b",
                model_type=ModelType.ANALYTICAL,
                capabilities=[ModelCapability.REASONING, ModelCapability.MULTILINGUAL, ModelCapability.LONG_CONTEXT],
                max_context=32768,
                temperature_range=(0.0, 1.0),
                quality_score=0.87,
                response_time_avg=1.4
            )
        ]
        
        for model in models:
            self._models[model.model_id] = model
            self._model_performance[model.model_id] = {
                'success_rate': 0.95,
                'avg_response_time': model.response_time_avg,
                'user_satisfaction': model.quality_score,
                'total_requests': 0
            }
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of available models with their capabilities"""
        return [
            {
                'model_id': model.model_id,
                'name': model.name,
                'type': model.model_type.value,
                'capabilities': [cap.value for cap in model.capabilities],
                'max_context': model.max_context,
                'quality_score': model.quality_score,
                'performance': self._model_performance[model.model_id]
            }
            for model in self._models.values()
        ]
    
    def select_best_model(self, 
                         query: str, 
                         context_length: int = 0,
                         required_capabilities: List[ModelCapability] = None,
                         preferred_type: ModelType = None) -> str:
        """
        Intelligently select the best model for a given query
        
        Args:
            query: User query to analyze
            context_length: Required context length
            required_capabilities: Required model capabilities
            preferred_type: Preferred model type
            
        Returns:
            Best model ID for the query
        """
        try:
            # Analyze query characteristics
            query_analysis = self._analyze_query(query)
            
            # Score models based on suitability
            model_scores = {}
            
            for model_id, model in self._models.items():
                score = 0.0
                
                # Base quality score
                score += model.quality_score * 0.3
                
                # Performance score
                perf = self._model_performance[model_id]
                score += perf['success_rate'] * 0.2
                score += (1.0 / max(perf['avg_response_time'], 0.1)) * 0.1
                
                # Context length compatibility
                if context_length <= model.max_context:
                    score += 0.2
                else:
                    score -= 0.5  # Penalize if context too long
                
                # Capability matching
                if required_capabilities:
                    matching_caps = len(set(required_capabilities) & set(model.capabilities))
                    score += (matching_caps / len(required_capabilities)) * 0.3
                
                # Type preference
                if preferred_type and model.model_type == preferred_type:
                    score += 0.2
                
                # Query-specific scoring
                if query_analysis['is_technical'] and ModelCapability.CODE_GENERATION in model.capabilities:
                    score += 0.15
                if query_analysis['is_creative'] and model.model_type == ModelType.CREATIVE:
                    score += 0.15
                if query_analysis['is_analytical'] and model.model_type == ModelType.ANALYTICAL:
                    score += 0.15
                
                model_scores[model_id] = score
            
            # Select best model
            best_model = max(model_scores.items(), key=lambda x: x[1])[0]
            
            logger.info(f"Selected model {best_model} for query with score {model_scores[best_model]:.3f}")
            return best_model
            
        except Exception as e:
            logger.error(f"Error selecting model: {e}")
            # Fallback to default model
            return settings.LLM_MODEL
    
    def _analyze_query(self, query: str) -> Dict[str, bool]:
        """Analyze query characteristics to help with model selection"""
        query_lower = query.lower()
        
        # Technical indicators
        technical_keywords = ['code', 'programming', 'function', 'api', 'database', 'algorithm', 'debug']
        is_technical = any(keyword in query_lower for keyword in technical_keywords)
        
        # Creative indicators
        creative_keywords = ['story', 'creative', 'imagine', 'design', 'brainstorm', 'idea']
        is_creative = any(keyword in query_lower for keyword in creative_keywords)
        
        # Analytical indicators
        analytical_keywords = ['analyze', 'compare', 'calculate', 'data', 'statistics', 'report']
        is_analytical = any(keyword in query_lower for keyword in analytical_keywords)
        
        # Complex reasoning indicators
        reasoning_keywords = ['why', 'how', 'explain', 'because', 'reason', 'logic']
        needs_reasoning = any(keyword in query_lower for keyword in reasoning_keywords)
        
        return {
            'is_technical': is_technical,
            'is_creative': is_creative,
            'is_analytical': is_analytical,
            'needs_reasoning': needs_reasoning,
            'is_complex': len(query.split()) > 20 or needs_reasoning
        }
    
    def get_llm_instance(self, model_id: str, temperature: float = 0.1, max_context: int = 8192) -> ChatOllama:
        """Get or create LLM instance for specified model"""
        cache_key = f"{model_id}_{temperature}_{max_context}"
        
        if cache_key not in self._llm_instances:
            if model_id not in self._models:
                logger.warning(f"Model {model_id} not found, using default")
                model_id = settings.LLM_MODEL
            
            model_config = self._models.get(model_id)
            if model_config:
                # Ensure temperature is within model's range
                min_temp, max_temp = model_config.temperature_range
                temperature = max(min_temp, min(max_temp, temperature))
                
                # Ensure context length is within model's limits
                max_context = min(max_context, model_config.max_context)
            
            self._llm_instances[cache_key] = ChatOllama(
                model=model_id,
                temperature=temperature,
                num_ctx=max_context
            )
            
            logger.info(f"Created LLM instance for {model_id} with temp={temperature}, ctx={max_context}")
        
        return self._llm_instances[cache_key]
    
    def update_model_performance(self, model_id: str, response_time: float, success: bool, user_rating: float = None):
        """Update model performance metrics"""
        if model_id in self._model_performance:
            perf = self._model_performance[model_id]
            
            # Update response time (moving average)
            perf['avg_response_time'] = (perf['avg_response_time'] * 0.9) + (response_time * 0.1)
            
            # Update success rate
            total_requests = perf['total_requests']
            current_success_rate = perf['success_rate']
            new_success_rate = (current_success_rate * total_requests + (1.0 if success else 0.0)) / (total_requests + 1)
            perf['success_rate'] = new_success_rate
            
            # Update user satisfaction if provided
            if user_rating is not None:
                perf['user_satisfaction'] = (perf['user_satisfaction'] * 0.9) + (user_rating * 0.1)
            
            perf['total_requests'] += 1
    
    async def check_model_availability(self, model_id: str) -> bool:
        """Check if a model is available in Ollama"""
        try:
            # This would typically make an API call to check model availability
            # For now, we'll assume models in our config are available
            return model_id in self._models
        except Exception as e:
            logger.error(f"Error checking model availability for {model_id}: {e}")
            return False
