#!/bin/bash

echo "🚀 Starting Website Chatbot Test Environment..."
echo ""

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install dependencies if needed
if [ ! -f "venv/pyvenv.cfg" ]; then
    echo "📥 Installing dependencies..."
    pip install -r requirements.txt
fi

# Start the chatbot service in background
echo "🤖 Starting Website Chatbot Service..."
python start.py &
CHATBOT_PID=$!

# Wait for service to start
echo "⏳ Waiting for service to start..."
sleep 5

# Check if service is running
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ Chatbot service is running on http://localhost:8000"
    echo "📖 API Documentation: http://localhost:8000/docs"
else
    echo "❌ Failed to start chatbot service"
    exit 1
fi

# Start test page server
echo "🌐 Starting test page server..."
python3 -m http.server 8080 --directory . &
TEST_SERVER_PID=$!

echo "✅ Test page server is running on http://localhost:8080"
echo "🧪 Test Dashboard: http://localhost:8080/test_dashboard.html"
echo ""
echo "🎯 Test Environment Ready!"
echo "   - Backend Service: http://localhost:8000"
echo "   - Test Dashboard: http://localhost:8080/test_dashboard.html"
echo "   - API Docs: http://localhost:8000/docs"
echo ""
echo "Press Ctrl+C to stop all services"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping services..."
    kill $CHATBOT_PID 2>/dev/null
    kill $TEST_SERVER_PID 2>/dev/null
    echo "✅ Services stopped"
    exit 0
}

# Set trap to cleanup on exit
trap cleanup SIGINT SIGTERM

# Keep script running
wait 