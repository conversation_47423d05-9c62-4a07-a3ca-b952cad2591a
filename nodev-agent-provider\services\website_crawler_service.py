import asyncio
import logging
import re
import time
from typing import List, Dict, Any, Optional, Set
from urllib.parse import urljoin, urlparse
from pathlib import Path

import aiohttp
from bs4 import BeautifulSoup
from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter

from utils.config import settings
from utils.logger import get_logger

logger = get_logger(__name__)

class WebsiteCrawlerService:
    """Service for crawling websites and extracting content for chatbot training"""
    
    def __init__(self):
        """Initialize the website crawler service"""
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
            is_separator_regex=False,
        )
        
        # Common content selectors for different types of content
        self.content_selectors = {
            'main_content': [
                'main', 'article', '.content', '.main-content', '.post-content',
                '.entry-content', '.page-content', '.article-content'
            ],
            'navigation': [
                'nav', '.navigation', '.menu', '.navbar', '.breadcrumb'
            ],
            'sidebar': [
                'aside', '.sidebar', '.side-content', '.widget'
            ],
            'footer': [
                'footer', '.footer', '.site-footer'
            ]
        }
        
        # Content types to extract
        self.extract_content_types = ['main_content', 'navigation', 'sidebar']
    
    async def crawl_website(self, website_url: str, max_pages: int = None, max_depth: int = None) -> List[Document]:
        """
        Crawl a website and extract content for chatbot training
        
        Args:
            website_url: Base URL of the website
            max_pages: Maximum number of pages to crawl
            max_depth: Maximum depth of crawling
            
        Returns:
            List of documents extracted from the website
        """
        if max_pages is None:
            max_pages = settings.MAX_PAGES_PER_WEBSITE
        if max_depth is None:
            max_depth = settings.MAX_CRAWL_DEPTH
        
        try:
            logger.info(f"Starting website crawl: {website_url} (max_pages: {max_pages}, max_depth: {max_depth})")
            
            # Initialize crawling state
            visited_urls: Set[str] = set()
            urls_to_visit: List[tuple] = [(website_url, 0)]  # (url, depth)
            extracted_documents: List[Document] = []
            
            # Add robots.txt and sitemap processing
            await self._process_robots_and_sitemap(website_url, urls_to_visit, visited_urls)
            
            # Main crawling loop
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={'User-Agent': 'WebsiteChatbotCrawler/1.0'}
            ) as session:
                
                while urls_to_visit and len(extracted_documents) < max_pages:
                    current_url, depth = urls_to_visit.pop(0)
                    
                    if current_url in visited_urls or depth > max_depth:
                        continue
                    
                    try:
                        # Crawl the page
                        page_documents = await self._crawl_page(
                            session, current_url, website_url, depth
                        )
                        
                        if page_documents:
                            extracted_documents.extend(page_documents)
                            
                            # Extract links for further crawling
                            if depth < max_depth:
                                new_urls = await self._extract_links(session, current_url, website_url)
                                for new_url in new_urls:
                                    if new_url not in visited_urls:
                                        urls_to_visit.append((new_url, depth + 1))
                        
                        visited_urls.add(current_url)
                        
                        # Respect crawl delay
                        await asyncio.sleep(settings.CRAWL_DELAY)
                        
                    except Exception as e:
                        logger.warning(f"Error crawling {current_url}: {e}")
                        visited_urls.add(current_url)
                        continue
            
            logger.info(f"Website crawl completed: {len(extracted_documents)} documents extracted from {len(visited_urls)} pages")
            return extracted_documents
            
        except Exception as e:
            logger.error(f"Error during website crawl of {website_url}: {e}")
            return []
    
    async def _process_robots_and_sitemap(self, base_url: str, urls_to_visit: List, visited_urls: Set[str]):
        """Process robots.txt and sitemap for better crawling"""
        try:
            # Check robots.txt
            robots_url = urljoin(base_url, '/robots.txt')
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(robots_url) as response:
                        if response.status == 200:
                            robots_content = await response.text()
                            # Parse robots.txt and add allowed URLs
                            allowed_urls = self._parse_robots_txt(robots_content, base_url)
                            for url in allowed_urls:
                                if url not in visited_urls:
                                    urls_to_visit.append((url, 0))
            except:
                pass
            
            # Check sitemap
            sitemap_urls = [
                urljoin(base_url, '/sitemap.xml'),
                urljoin(base_url, '/sitemap_index.xml'),
                urljoin(base_url, '/sitemap/'),
            ]
            
            for sitemap_url in sitemap_urls:
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(sitemap_url) as response:
                            if response.status == 200:
                                sitemap_content = await response.text()
                                sitemap_urls = self._parse_sitemap(sitemap_content, base_url)
                                for url in sitemap_urls:
                                    if url not in visited_urls:
                                        urls_to_visit.append((url, 0))
                                break
                except:
                    continue
                    
        except Exception as e:
            logger.warning(f"Error processing robots.txt/sitemap: {e}")
    
    async def _crawl_page(self, session: aiohttp.ClientSession, url: str, base_url: str, depth: int) -> List[Document]:
        """Crawl a single page and extract content"""
        try:
            async with session.get(url) as response:
                if response.status != 200:
                    return []
                
                content_type = response.headers.get('content-type', '')
                if 'text/html' not in content_type:
                    return []
                
                html_content = await response.text()
                return self._extract_content_from_html(html_content, url, base_url, depth)
                
        except Exception as e:
            logger.warning(f"Error crawling page {url}: {e}")
            return []
    
    def _extract_content_from_html(self, html_content: str, url: str, base_url: str, depth: int) -> List[Document]:
        """Extract meaningful content from HTML"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style", "nav", "footer", "header"]):
                script.decompose()
            
            extracted_content = []
            
            # Extract main content
            for selector_type in self.extract_content_types:
                for selector in self.content_selectors[selector_type]:
                    elements = soup.select(selector)
                    for element in elements:
                        text = element.get_text(strip=True)
                        if len(text) > 100:  # Only meaningful content
                            extracted_content.append({
                                'text': text,
                                'type': selector_type,
                                'selector': selector,
                                'url': url
                            })
            
            # Extract page title and meta description
            title = soup.find('title')
            if title:
                title_text = title.get_text(strip=True)
                if title_text:
                    extracted_content.append({
                        'text': f"Page Title: {title_text}",
                        'type': 'title',
                        'selector': 'title',
                        'url': url
                    })
            
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc and meta_desc.get('content'):
                desc_text = meta_desc.get('content', '').strip()
                if desc_text:
                    extracted_content.append({
                        'text': f"Page Description: {desc_text}",
                        'type': 'description',
                        'selector': 'meta[name="description"]',
                        'url': url
                    })
            
            # Convert to documents
            documents = []
            for content in extracted_content:
                # Split content into chunks
                chunks = self.text_splitter.split_text(content['text'])
                
                for i, chunk in enumerate(chunks):
                    if len(chunk.strip()) > 50:  # Filter out very short chunks
                        doc = Document(
                            page_content=chunk,
                            metadata={
                                'source': url,
                                'type': 'website_content',
                                'content_type': content['type'],
                                'selector': content['selector'],
                                'chunk_index': i,
                                'total_chunks': len(chunks),
                                'depth': depth,
                                'base_url': base_url,
                                'extracted_at': time.time()
                            }
                        )
                        documents.append(doc)
            
            return documents
            
        except Exception as e:
            logger.warning(f"Error extracting content from {url}: {e}")
            return []
    
    async def _extract_links(self, session: aiohttp.ClientSession, page_url: str, base_url: str) -> List[str]:
        """Extract links from a page for further crawling"""
        try:
            async with session.get(page_url) as response:
                if response.status != 200:
                    return []
                
                html_content = await response.text()
                soup = BeautifulSoup(html_content, 'html.parser')
                
                links = []
                for link in soup.find_all('a', href=True):
                    href = link['href']
                    absolute_url = urljoin(page_url, href)
                    
                    # Only include links to the same domain
                    if self._is_same_domain(absolute_url, base_url):
                        # Filter out common non-content URLs
                        if not self._is_excluded_url(absolute_url):
                            links.append(absolute_url)
                
                return list(set(links))  # Remove duplicates
                
        except Exception as e:
            logger.warning(f"Error extracting links from {page_url}: {e}")
            return []
    
    def _is_same_domain(self, url: str, base_url: str) -> bool:
        """Check if URL is from the same domain"""
        try:
            url_domain = urlparse(url).netloc
            base_domain = urlparse(base_url).netloc
            return url_domain == base_domain
        except:
            return False
    
    def _is_excluded_url(self, url: str) -> bool:
        """Check if URL should be excluded from crawling"""
        excluded_patterns = [
            r'\.(pdf|doc|docx|xls|xlsx|ppt|pptx|zip|rar|exe|dmg)$',
            r'#.*$',  # Anchor links
            r'mailto:',  # Email links
            r'tel:',  # Phone links
            r'javascript:',  # JavaScript links
            r'/admin/',  # Admin areas
            r'/login',  # Login pages
            r'/logout',  # Logout pages
            r'/cart',  # Shopping cart
            r'/checkout',  # Checkout pages
        ]
        
        for pattern in excluded_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return True
        
        return False
    
    def _parse_robots_txt(self, content: str, base_url: str) -> List[str]:
        """Parse robots.txt and extract allowed URLs"""
        allowed_urls = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if line.startswith('Allow:'):
                path = line[7:].strip()
                if path:
                    full_url = urljoin(base_url, path)
                    allowed_urls.append(full_url)
        
        return allowed_urls
    
    def _parse_sitemap(self, content: str, base_url: str) -> List[str]:
        """Parse sitemap XML and extract URLs"""
        urls = []
        try:
            soup = BeautifulSoup(content, 'xml')
            
            # Handle both sitemap index and regular sitemap
            if soup.find('sitemapindex'):
                # Sitemap index
                for sitemap in soup.find_all('sitemap'):
                    loc = sitemap.find('loc')
                    if loc:
                        urls.append(loc.get_text())
            else:
                # Regular sitemap
                for url in soup.find_all('url'):
                    loc = url.find('loc')
                    if loc:
                        urls.append(loc.get_text())
                        
        except Exception as e:
            logger.warning(f"Error parsing sitemap: {e}")
        
        return urls
    
    async def crawl_and_index_website(self, website_url: str, chatbot_id: str, max_pages: int = None) -> Dict[str, Any]:
        """
        Crawl website and index content into vector store
        
        Args:
            website_url: Website URL to crawl
            chatbot_id: Chatbot ID for the vector store
            max_pages: Maximum pages to crawl
            
        Returns:
            Dictionary with crawl results
        """
        try:
            # Crawl the website
            documents = await self.crawl_website(website_url, max_pages)
            
            if not documents:
                return {
                    'success': False,
                    'message': 'No content extracted from website',
                    'documents_count': 0
                }
            
            # Index documents into vector store
            from services.vectorstore_service import VectorStoreService
            vectorstore_service = VectorStoreService()
            
            documents_added = vectorstore_service.add_website_documents(documents, chatbot_id)
            
            return {
                'success': True,
                'message': f'Successfully crawled and indexed {documents_added} documents',
                'documents_count': documents_added,
                'website_url': website_url,
                'chatbot_id': chatbot_id
            }
            
        except Exception as e:
            logger.error(f"Error in crawl_and_index_website: {e}")
            return {
                'success': False,
                'message': f'Error during website crawling: {str(e)}',
                'documents_count': 0
            } 