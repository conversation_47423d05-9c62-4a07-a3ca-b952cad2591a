{"id": "conv_3d0a056dc1ea", "chatbot_id": "chatbot_2f88e98b2463", "user_id": null, "session_id": "simple_test_session", "started_at": "2025-08-24 13:11:51.197122", "last_activity": "2025-08-24 13:12:05.199290", "ended_at": null, "is_active": true, "total_messages": 8, "user_satisfaction": null, "resolution_time": null, "lead_qualified": true, "lead_score": 0.5, "human_handoff_occurred": false, "messages": [{"id": "msg_2e753b8ca5d8", "conversation_id": "conv_3d0a056dc1ea", "content": "What services do you offer?", "role": "user", "message_type": "text", "timestamp": "2025-08-24 13:11:53.245286", "processing_time": 5.234017372131348, "detected_intent": "product_inquiry", "confidence_score": 0.5, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": false, "lead_score": 0.0, "requires_human_handoff": false}, {"id": "msg_40bd8930ea93", "conversation_id": "conv_3d0a056dc1ea", "content": "TechCorp Solutions offers three main services:\n\n🤖 **AI Chatbot Development** - Create intelligent chatbots with advanced RAG capabilities, multi-model support, and tool calling features.\n\n💻 **Custom Software Development** - Full-stack web applications, mobile apps, and enterprise software solutions using modern technologies.\n\n🎓 **AI Consulting & Training** - Strategic AI implementation guidance, team training, and ongoing support.\n\nWould you like to know more about any of these services?", "role": "assistant", "message_type": "text", "timestamp": "2025-08-24 13:11:58.478494", "processing_time": null, "detected_intent": null, "confidence_score": null, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": null, "lead_score": null, "requires_human_handoff": false}, {"id": "msg_0436baea228f", "conversation_id": "conv_3d0a056dc1ea", "content": "How much does the Professional plan cost?", "role": "user", "message_type": "text", "timestamp": "2025-08-24 13:12:00.539034", "processing_time": 0.19010710716247559, "detected_intent": "pricing_inquiry", "confidence_score": 0.9, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": true, "lead_score": 0.5, "requires_human_handoff": false}, {"id": "msg_6e3389c52dce", "conversation_id": "conv_3d0a056dc1ea", "content": "TechCorp Solutions offers three main services:\n\n🤖 **AI Chatbot Development** - Create intelligent chatbots with advanced RAG capabilities, multi-model support, and tool calling features.\n\n💻 **Custom Software Development** - Full-stack web applications, mobile apps, and enterprise software solutions using modern technologies.\n\n🎓 **AI Consulting & Training** - Strategic AI implementation guidance, team training, and ongoing support.\n\nWould you like to know more about any of these services?", "role": "assistant", "message_type": "text", "timestamp": "2025-08-24 13:12:00.728216", "processing_time": null, "detected_intent": null, "confidence_score": null, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": null, "lead_score": null, "requires_human_handoff": false}, {"id": "msg_88bad80cbe3e", "conversation_id": "conv_3d0a056dc1ea", "content": "What are your business hours?", "role": "user", "message_type": "text", "timestamp": "2025-08-24 13:12:02.779075", "processing_time": 0.1819901466369629, "detected_intent": "unknown", "confidence_score": 0.3, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": false, "lead_score": 0.0, "requires_human_handoff": false}, {"id": "msg_0b1dde1d65d2", "conversation_id": "conv_3d0a056dc1ea", "content": "TechCorp Solutions offers three main services:\n\n🤖 **AI Chatbot Development** - Create intelligent chatbots with advanced RAG capabilities, multi-model support, and tool calling features.\n\n💻 **Custom Software Development** - Full-stack web applications, mobile apps, and enterprise software solutions using modern technologies.\n\n🎓 **AI Consulting & Training** - Strategic AI implementation guidance, team training, and ongoing support.\n\nWould you like to know more about any of these services?", "role": "assistant", "message_type": "text", "timestamp": "2025-08-24 13:12:02.960127", "processing_time": null, "detected_intent": null, "confidence_score": null, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": null, "lead_score": null, "requires_human_handoff": false}, {"id": "msg_c1a92a5fe2e6", "conversation_id": "conv_3d0a056dc1ea", "content": "How can I contact you?", "role": "user", "message_type": "text", "timestamp": "2025-08-24 13:12:05.016728", "processing_time": 0.1835029125213623, "detected_intent": "lead_qualification", "confidence_score": 0.5, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": true, "lead_score": 0.5, "requires_human_handoff": false}, {"id": "msg_f9f922a7b4fe", "conversation_id": "conv_3d0a056dc1ea", "content": "You can reach us at:\n\n📧 **Email**: <EMAIL>\n📞 **Phone**: +****************\n🕒 **Hours**: Monday-Friday, 9:00 AM - 6:00 PM EST\n\nWe'd be happy to discuss your specific needs!", "role": "assistant", "message_type": "text", "timestamp": "2025-08-24 13:12:05.199233", "processing_time": null, "detected_intent": null, "confidence_score": null, "entities": null, "user_ip": null, "user_agent": null, "session_id": "simple_test_session", "is_lead_qualified": null, "lead_score": null, "requires_human_handoff": false}]}