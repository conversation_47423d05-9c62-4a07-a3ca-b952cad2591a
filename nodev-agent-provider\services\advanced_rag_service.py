"""
Advanced RAG Service with Hybrid Search, Re-ranking, and Query Enhancement
"""

import asyncio
import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from langchain_core.documents import Document
from langchain_core.prompts import Chat<PERSON><PERSON>pt<PERSON>emplate
from langchain_core.runnables import RunnablePassthrough
from langchain_core.output_parsers import StrOutputParser

from services.vectorstore_service import VectorStoreService
from services.ai_model_service import AIModelService, ModelCapability, ModelType
from utils.config import settings
from utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class SearchResult:
    """Enhanced search result with metadata"""
    document: Document
    relevance_score: float
    search_type: str  # 'semantic', 'keyword', 'hybrid'
    rerank_score: Optional[float] = None

@dataclass
class QueryAnalysis:
    """Query analysis results"""
    original_query: str
    expanded_queries: List[str]
    intent: str
    complexity: str  # 'simple', 'medium', 'complex'
    requires_multi_hop: bool
    keywords: List[str]
    entities: List[str]

class AdvancedRAGService:
    """Advanced RAG service with hybrid search and intelligent query processing"""
    
    def __init__(self):
        """Initialize the advanced RAG service"""
        self.vectorstore_service = VectorStoreService()
        self.ai_model_service = AIModelService()
        self._query_cache: Dict[str, List[SearchResult]] = {}
        self._reranker_model = None  # Could be initialized with a cross-encoder model
        
    async def enhanced_search(self, 
                            query: str, 
                            chatbot_id: str,
                            k: int = 5,
                            use_hybrid: bool = True,
                            use_reranking: bool = True,
                            expand_query: bool = True) -> List[SearchResult]:
        """
        Perform enhanced search with multiple strategies
        
        Args:
            query: User query
            chatbot_id: Chatbot identifier
            k: Number of results to return
            use_hybrid: Whether to use hybrid search
            use_reranking: Whether to apply re-ranking
            expand_query: Whether to expand the query
            
        Returns:
            List of enhanced search results
        """
        try:
            # Analyze query
            query_analysis = await self._analyze_query(query)
            
            # Expand query if needed
            search_queries = [query]
            if expand_query and query_analysis.complexity in ['medium', 'complex']:
                search_queries.extend(query_analysis.expanded_queries)
            
            # Perform searches
            all_results = []
            
            for search_query in search_queries:
                if use_hybrid:
                    # Hybrid search (semantic + keyword)
                    semantic_results = await self._semantic_search(search_query, chatbot_id, k)
                    keyword_results = await self._keyword_search(search_query, chatbot_id, k)
                    
                    # Combine and deduplicate
                    combined_results = self._combine_search_results(semantic_results, keyword_results)
                    all_results.extend(combined_results)
                else:
                    # Semantic search only
                    semantic_results = await self._semantic_search(search_query, chatbot_id, k)
                    all_results.extend(semantic_results)
            
            # Remove duplicates
            unique_results = self._deduplicate_results(all_results)
            
            # Re-rank results if enabled
            if use_reranking and len(unique_results) > 1:
                unique_results = await self._rerank_results(query, unique_results)
            
            # Return top k results
            return unique_results[:k]
            
        except Exception as e:
            logger.error(f"Error in enhanced search: {e}")
            # Fallback to basic search
            return await self._basic_search(query, chatbot_id, k)
    
    async def _analyze_query(self, query: str) -> QueryAnalysis:
        """Analyze query to understand intent and complexity"""
        try:
            # Basic keyword extraction
            keywords = self._extract_keywords(query)
            
            # Simple entity extraction (could be enhanced with NER)
            entities = self._extract_entities(query)
            
            # Determine complexity
            complexity = self._determine_complexity(query)
            
            # Check if multi-hop reasoning is needed
            requires_multi_hop = self._requires_multi_hop(query)
            
            # Generate expanded queries
            expanded_queries = await self._generate_expanded_queries(query)
            
            # Determine intent
            intent = self._classify_intent(query)
            
            return QueryAnalysis(
                original_query=query,
                expanded_queries=expanded_queries,
                intent=intent,
                complexity=complexity,
                requires_multi_hop=requires_multi_hop,
                keywords=keywords,
                entities=entities
            )
            
        except Exception as e:
            logger.error(f"Error analyzing query: {e}")
            return QueryAnalysis(
                original_query=query,
                expanded_queries=[],
                intent="general",
                complexity="simple",
                requires_multi_hop=False,
                keywords=[],
                entities=[]
            )
    
    def _extract_keywords(self, query: str) -> List[str]:
        """Extract important keywords from query"""
        # Simple keyword extraction (could be enhanced with TF-IDF or other methods)
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'what', 'where', 'when', 'why', 'how', 'who', 'which'}
        
        words = re.findall(r'\b\w+\b', query.lower())
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        
        return keywords[:10]  # Return top 10 keywords
    
    def _extract_entities(self, query: str) -> List[str]:
        """Extract named entities from query (simplified)"""
        # Simple entity extraction - could be enhanced with spaCy or other NER tools
        entities = []
        
        # Look for capitalized words (potential proper nouns)
        words = query.split()
        for word in words:
            if word[0].isupper() and len(word) > 2:
                entities.append(word)
        
        # Look for numbers
        numbers = re.findall(r'\b\d+\b', query)
        entities.extend(numbers)
        
        return entities
    
    def _determine_complexity(self, query: str) -> str:
        """Determine query complexity"""
        word_count = len(query.split())
        question_words = ['what', 'where', 'when', 'why', 'how', 'who', 'which']
        has_question_words = any(word in query.lower() for word in question_words)
        
        if word_count < 5:
            return "simple"
        elif word_count < 15 and not has_question_words:
            return "medium"
        else:
            return "complex"
    
    def _requires_multi_hop(self, query: str) -> bool:
        """Check if query requires multi-hop reasoning"""
        multi_hop_indicators = [
            'compare', 'difference', 'relationship', 'connection', 'because', 'therefore',
            'as a result', 'consequently', 'in addition', 'furthermore', 'moreover'
        ]
        
        return any(indicator in query.lower() for indicator in multi_hop_indicators)
    
    async def _generate_expanded_queries(self, query: str) -> List[str]:
        """Generate expanded queries for better search coverage"""
        try:
            # Use AI model to generate query variations
            model_id = self.ai_model_service.select_best_model(
                query, 
                required_capabilities=[ModelCapability.REASONING]
            )
            
            llm = self.ai_model_service.get_llm_instance(model_id, temperature=0.3)
            
            prompt = ChatPromptTemplate.from_template("""
            Given the following user query, generate 2-3 alternative ways to ask the same question that might help find relevant information:

            Original Query: {query}

            Generate alternative queries that:
            1. Use different keywords but ask the same thing
            2. Are more specific or more general
            3. Focus on different aspects of the question

            Alternative Queries:
            1.
            2.
            3.
            """)
            
            chain = prompt | llm | StrOutputParser()
            response = await asyncio.to_thread(chain.invoke, {"query": query})
            
            # Parse the response to extract alternative queries
            lines = response.strip().split('\n')
            expanded_queries = []
            
            for line in lines:
                if line.strip() and (line.strip().startswith('1.') or line.strip().startswith('2.') or line.strip().startswith('3.')):
                    expanded_query = line.strip()[2:].strip()
                    if expanded_query and expanded_query != query:
                        expanded_queries.append(expanded_query)
            
            return expanded_queries[:3]  # Return max 3 expanded queries
            
        except Exception as e:
            logger.error(f"Error generating expanded queries: {e}")
            return []
    
    def _classify_intent(self, query: str) -> str:
        """Classify query intent"""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ['price', 'cost', 'buy', 'purchase', 'order']):
            return "purchase"
        elif any(word in query_lower for word in ['support', 'help', 'problem', 'issue', 'error']):
            return "support"
        elif any(word in query_lower for word in ['how', 'tutorial', 'guide', 'instructions']):
            return "how_to"
        elif any(word in query_lower for word in ['what', 'information', 'about', 'details']):
            return "information"
        elif any(word in query_lower for word in ['contact', 'phone', 'email', 'address']):
            return "contact"
        else:
            return "general"
    
    async def _semantic_search(self, query: str, chatbot_id: str, k: int) -> List[SearchResult]:
        """Perform semantic search using vector similarity"""
        try:
            documents = self.vectorstore_service.search_website_documents(query, chatbot_id, k=k*2)  # Get more for better selection
            
            results = []
            for i, doc in enumerate(documents):
                # Calculate relevance score (simplified - in practice, this would come from the vector store)
                relevance_score = max(0.1, 1.0 - (i * 0.1))  # Decreasing score based on rank
                
                results.append(SearchResult(
                    document=doc,
                    relevance_score=relevance_score,
                    search_type="semantic"
                ))
            
            return results
            
        except Exception as e:
            logger.error(f"Error in semantic search: {e}")
            return []
    
    async def _keyword_search(self, query: str, chatbot_id: str, k: int) -> List[SearchResult]:
        """Perform keyword-based search"""
        try:
            # Extract keywords from query
            keywords = self._extract_keywords(query)
            
            if not keywords:
                return []
            
            # Get all documents for the chatbot (simplified approach)
            all_documents = self.vectorstore_service.search_website_documents("", chatbot_id, k=100)
            
            # Score documents based on keyword matches
            scored_results = []
            
            for doc in all_documents:
                content_lower = doc.page_content.lower()
                keyword_score = 0.0
                
                for keyword in keywords:
                    # Count keyword occurrences
                    occurrences = content_lower.count(keyword.lower())
                    keyword_score += occurrences * (1.0 / len(keywords))
                
                if keyword_score > 0:
                    scored_results.append(SearchResult(
                        document=doc,
                        relevance_score=min(1.0, keyword_score),
                        search_type="keyword"
                    ))
            
            # Sort by score and return top k
            scored_results.sort(key=lambda x: x.relevance_score, reverse=True)
            return scored_results[:k]
            
        except Exception as e:
            logger.error(f"Error in keyword search: {e}")
            return []
    
    def _combine_search_results(self, semantic_results: List[SearchResult], keyword_results: List[SearchResult]) -> List[SearchResult]:
        """Combine semantic and keyword search results"""
        combined = []
        
        # Add semantic results with boosted scores
        for result in semantic_results:
            result.relevance_score *= 1.2  # Boost semantic results
            result.search_type = "hybrid"
            combined.append(result)
        
        # Add keyword results
        for result in keyword_results:
            # Check if document already exists in semantic results
            existing = next((r for r in combined if r.document.page_content == result.document.page_content), None)
            
            if existing:
                # Combine scores
                existing.relevance_score = (existing.relevance_score + result.relevance_score) / 2
            else:
                result.search_type = "hybrid"
                combined.append(result)
        
        return combined
    
    def _deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """Remove duplicate results based on content similarity"""
        unique_results = []
        seen_content = set()
        
        for result in results:
            # Simple deduplication based on content hash
            content_hash = hash(result.document.page_content[:200])  # Use first 200 chars for hash
            
            if content_hash not in seen_content:
                seen_content.add(content_hash)
                unique_results.append(result)
        
        # Sort by relevance score
        unique_results.sort(key=lambda x: x.relevance_score, reverse=True)
        return unique_results
    
    async def _rerank_results(self, query: str, results: List[SearchResult]) -> List[SearchResult]:
        """Re-rank results using cross-encoder or LLM-based scoring"""
        try:
            # Use AI model for re-ranking
            model_id = self.ai_model_service.select_best_model(
                query,
                required_capabilities=[ModelCapability.REASONING]
            )
            
            llm = self.ai_model_service.get_llm_instance(model_id, temperature=0.1)
            
            # Score each result
            for result in results:
                prompt = ChatPromptTemplate.from_template("""
                Rate how relevant this document is to the user's query on a scale of 0.0 to 1.0.
                
                Query: {query}
                
                Document: {document}
                
                Consider:
                - Direct relevance to the query
                - Quality and completeness of information
                - Clarity and usefulness
                
                Relevance Score (0.0-1.0):
                """)
                
                chain = prompt | llm | StrOutputParser()
                
                try:
                    response = await asyncio.to_thread(chain.invoke, {
                        "query": query,
                        "document": result.document.page_content[:1000]  # Limit content length
                    })
                    
                    # Extract score from response
                    score_match = re.search(r'(\d+\.?\d*)', response)
                    if score_match:
                        rerank_score = float(score_match.group(1))
                        result.rerank_score = min(1.0, max(0.0, rerank_score))
                    else:
                        result.rerank_score = result.relevance_score
                        
                except Exception as e:
                    logger.error(f"Error re-ranking result: {e}")
                    result.rerank_score = result.relevance_score
            
            # Sort by re-rank score
            results.sort(key=lambda x: x.rerank_score or x.relevance_score, reverse=True)
            return results
            
        except Exception as e:
            logger.error(f"Error in re-ranking: {e}")
            return results
    
    async def _basic_search(self, query: str, chatbot_id: str, k: int) -> List[SearchResult]:
        """Fallback basic search"""
        try:
            documents = self.vectorstore_service.search_website_documents(query, chatbot_id, k=k)
            
            return [
                SearchResult(
                    document=doc,
                    relevance_score=max(0.1, 1.0 - (i * 0.1)),
                    search_type="basic"
                )
                for i, doc in enumerate(documents)
            ]
            
        except Exception as e:
            logger.error(f"Error in basic search: {e}")
            return []
