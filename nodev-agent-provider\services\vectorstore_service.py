import os
import logging
from typing import List, Optional, Dict, Any
from pathlib import Path

from langchain_ollama import OllamaEmbeddings
from langchain_qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from langchain_core.documents import Document

from utils.config import settings
from utils.logger import get_logger

logger = get_logger(__name__)

class VectorStoreService:
    """Service for managing vector stores and document operations"""
    
    def __init__(self):
        """Initialize the vector store service"""
        self.embedding_function = OllamaEmbeddings(model=settings.EMBED_MODEL)
        self._vectorstores: Dict[str, QdrantVectorStore] = {}
        
        # Use an in-memory Qdrant client to avoid lock issues during development
        try:
            # First try to use file-based storage
            self._client = QdrantClient(path=settings.QDRANT_PATH)
            logger.info("Using file-based Qdrant storage")
        except Exception as e:
            logger.warning(f"File-based Qdrant failed, using in-memory: {e}")
            # Fallback to in-memory storage
            self._client = QdrantClient(":memory:")
            logger.info("Using in-memory Qdrant storage")
        
        # Ensure base directories exist
        os.makedirs(settings.QDRANT_PATH, exist_ok=True)
        os.makedirs(settings.UPLOADS_PATH, exist_ok=True)
        os.makedirs(settings.DATA_PATH, exist_ok=True)
        
        # Initialize main collection
        self._ensure_collection_exists("main_collection")
    
    def _ensure_collection_exists(self, collection_name: str):
        """Ensure a collection exists, create if not"""
        try:
            collections = self._client.get_collections()
            collection_exists = any(c.name == collection_name for c in collections.collections)
            
            if not collection_exists:
                self._client.create_collection(
                    collection_name=collection_name,
                    vectors_config={
                        "size": 768,  # Default embedding size
                        "distance": "Cosine"
                    }
                )
                logger.info(f"Created Qdrant collection: {collection_name}")
        except Exception as e:
            logger.error(f"Error ensuring collection {collection_name} exists: {e}")
    
    def get_main_vectorstore(self) -> QdrantVectorStore:
        """Get the main application vector store"""
        return self._get_or_create_vectorstore("main", "main_collection")
    
    def get_website_vectorstore(self, chatbot_id: str) -> QdrantVectorStore:
        """Get or create a website-specific vector store"""
        collection_name = f"website_{chatbot_id}"
        return self._get_or_create_vectorstore(chatbot_id, collection_name)
    
    def _get_or_create_vectorstore(self, store_id: str, collection_name: str) -> QdrantVectorStore:
        """Get existing vector store or create new one"""
        if store_id in self._vectorstores:
            return self._vectorstores[store_id]
        
        try:
            # Ensure collection exists
            self._ensure_collection_exists(collection_name)
            
            # Create vector store using the single client
            vectorstore = QdrantVectorStore(
                client=self._client,
                collection_name=collection_name,
                embedding=self.embedding_function,
            )
            
            # Cache the vector store
            self._vectorstores[store_id] = vectorstore
            
            logger.info(f"Initialized vector store: {store_id} -> {collection_name}")
            return vectorstore
            
        except Exception as e:
            logger.error(f"Error creating vector store {store_id}: {e}")
            raise RuntimeError(f"Failed to create vector store: {e}")
    
    def add_documents(self, documents: List[Document], store_id: str = "main") -> int:
        """Add documents to a vector store"""
        try:
            vectorstore = self._get_or_create_vectorstore(store_id, f"{store_id}_collection")
            vectorstore.add_documents(documents)
            
            logger.info(f"Added {len(documents)} documents to vector store: {store_id}")
            return len(documents)
            
        except Exception as e:
            logger.error(f"Error adding documents to vector store {store_id}: {e}")
            raise RuntimeError(f"Failed to add documents: {e}")
    
    def add_website_documents(self, documents: List[Document], chatbot_id: str) -> int:
        """Add documents to a website-specific vector store"""
        try:
            vectorstore = self.get_website_vectorstore(chatbot_id)
            vectorstore.add_documents(documents)
            
            logger.info(f"Added {len(documents)} documents to website vector store: {chatbot_id}")
            return len(documents)
            
        except Exception as e:
            logger.error(f"Error adding documents to website vector store {chatbot_id}: {e}")
            raise RuntimeError(f"Failed to add documents: {e}")
    
    def search_documents(self, query: str, store_id: str = "main", limit: int = 5) -> List[Document]:
        """Search for documents in a vector store"""
        try:
            vectorstore = self._get_or_create_vectorstore(store_id, f"{store_id}_collection")
            documents = vectorstore.similarity_search(query, k=limit)
            
            logger.info(f"Found {len(documents)} documents for query: {query}")
            return documents
            
        except Exception as e:
            logger.error(f"Error searching vector store {store_id}: {e}")
            return []
    
    def search_website_documents(self, query: str, chatbot_id: str, limit: int = 5) -> List[Document]:
        """Search for documents in a website-specific vector store"""
        try:
            vectorstore = self.get_website_vectorstore(chatbot_id)
            documents = vectorstore.similarity_search(query, k=limit)
            
            logger.info(f"Found {len(documents)} documents for query: {query}")
            return documents
            
        except Exception as e:
            logger.error(f"Error searching website vector store {chatbot_id}: {e}")
            return []
    
    def delete_collection(self, collection_name: str) -> bool:
        """Delete a collection from Qdrant"""
        try:
            self._client.delete_collection(collection_name)
            logger.info(f"Deleted collection: {collection_name}")
            return True
        except Exception as e:
            logger.error(f"Error deleting collection {collection_name}: {e}")
            return False
    
    def delete_website_collection(self, chatbot_id: str) -> bool:
        """Delete a website-specific collection"""
        collection_name = f"website_{chatbot_id}"
        return self.delete_collection(collection_name)
    
    def get_collection_info(self, store_id: str) -> Dict[str, Any]:
        """Get information about a vector store collection"""
        try:
            if store_id == "main":
                collection_name = "main_collection"
            else:
                collection_name = f"website_{store_id}"
            
            collection_info = self._client.get_collection(collection_name)
            
            return {
                "store_id": store_id,
                "collection_name": collection_name,
                "vector_size": collection_info.config.params.vectors.size,
                "distance_metric": collection_info.config.params.vectors.distance,
                "total_points": collection_info.points_count,
                "status": collection_info.status
            }
            
        except Exception as e:
            logger.error(f"Error getting collection info for {store_id}: {e}")
            return {
                "store_id": store_id,
                "error": str(e)
            }
    
    def get_all_vectorstores(self) -> List[Dict[str, Any]]:
        """Get information about all vector stores"""
        try:
            collections = self._client.get_collections()
            vectorstore_info = []
            
            # Add main collection
            main_info = self.get_collection_info("main")
            if "error" not in main_info:
                vectorstore_info.append(main_info)
            
            # Add website collections
            for collection in collections.collections:
                if collection.name.startswith("website_"):
                    chatbot_id = collection.name.replace("website_", "")
                    website_info = self.get_collection_info(chatbot_id)
                    if "error" not in website_info:
                        vectorstore_info.append(website_info)
            
            return vectorstore_info
            
        except Exception as e:
            logger.error(f"Error getting all vector stores: {e}")
            return []
    
    def cleanup(self):
        """Clean up resources"""
        try:
            # Close the client connection
            if hasattr(self._client, 'close'):
                self._client.close()
            logger.info("Vector store service cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up vector store service: {e}") 