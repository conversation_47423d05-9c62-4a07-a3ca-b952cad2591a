from pydantic_settings import BaseSettings
from typing import List
import os

class Settings(BaseSettings):
    """Application configuration settings"""
    
    # Application
    APP_NAME: str = "Website Chatbot Service"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # Server
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # CORS
    ALLOWED_ORIGINS: List[str] = ["*"]
    
    # AI Models
    EMBED_MODEL: str = "nomic-embed-text"
    LLM_MODEL: str = "qwen3:8b"
    
    # Vector Database
    QDRANT_HOST: str = "localhost"
    QDRANT_PORT: int = 6333
    QDRANT_PATH: str = "qdrant_db"
    
    # MongoDB
    MONGODB_URL: str = "mongodb://iabhispatial:<EMAIL>:27017/spatialgrid-dev"
    MONGODB_DATABASE: str = "spatialgrid-dev"
    MONGODB_COLLECTION: str = "libraries"
    
    # File Storage
    UPLOADS_PATH: str = "uploads"
    DATA_PATH: str = "data"
    MAX_FILE_SIZE: int = 25 * 1024 * 1024  # 25MB
    ALLOWED_EXTENSIONS: List[str] = [".pdf", ".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff", ".webp"]
    
    # Security
    API_TOKEN: str = "changeme"
    SECRET_KEY: str = "your-secret-key-here"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Rate Limiting
    MAX_REQUESTS_PER_MINUTE: int = 100
    
    # Website Crawling
    MAX_CRAWL_DEPTH: int = 3
    MAX_PAGES_PER_WEBSITE: int = 100
    CRAWL_DELAY: float = 1.0  # seconds between requests
    
    # Chatbot
    MAX_CONVERSATION_LENGTH: int = 50
    DEFAULT_CHATBOT_PERSONALITY: str = "You are a helpful AI assistant for this website. Provide accurate, helpful information based on the website content."
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Global settings instance
settings = Settings() 