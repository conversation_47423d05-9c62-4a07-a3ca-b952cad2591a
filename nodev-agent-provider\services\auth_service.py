import logging
from typing import Op<PERSON>, Dict, Any
from datetime import datetime, timedelta

from fastapi import HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from utils.config import settings
from utils.logger import get_logger

logger = get_logger(__name__)

class AuthService:
    """Service for handling authentication and authorization"""
    
    def __init__(self):
        """Initialize the authentication service"""
        self.security = HTTPBearer()
        self._valid_tokens: Dict[str, Dict[str, Any]] = {}
        
        # Add default API token
        self._valid_tokens[settings.API_TOKEN] = {
            'user_id': 'admin',
            'role': 'admin',
            'created_at': datetime.utcnow(),
            'expires_at': None,  # Never expires
            'permissions': ['read', 'write', 'admin']
        }
    
    def validate_api_token(self, token: str) -> Dict[str, Any]:
        """
        Validate an API token
        
        Args:
            token: API token to validate
            
        Returns:
            Token information if valid
            
        Raises:
            HTTPException: If token is invalid
        """
        if token not in self._valid_tokens:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid API token"
            )
        
        token_info = self._valid_tokens[token]
        
        # Check if token has expired
        if token_info['expires_at'] and datetime.utcnow() > token_info['expires_at']:
            # Remove expired token
            del self._valid_tokens[token]
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API token has expired"
            )
        
        return token_info
    
    def create_api_token(self, user_id: str, role: str = 'user', expires_in_hours: int = 24) -> str:
        """
        Create a new API token
        
        Args:
            user_id: User identifier
            role: User role
            expires_in_hours: Token expiration time in hours
            
        Returns:
            New API token
        """
        import secrets
        
        # Generate secure token
        token = f"token_{secrets.token_urlsafe(32)}"
        
        # Calculate expiration
        expires_at = None
        if expires_in_hours > 0:
            expires_at = datetime.utcnow() + timedelta(hours=expires_in_hours)
        
        # Store token information
        self._valid_tokens[token] = {
            'user_id': user_id,
            'role': role,
            'created_at': datetime.utcnow(),
            'expires_at': expires_at,
            'permissions': self._get_permissions_for_role(role)
        }
        
        logger.info(f"Created API token for user: {user_id} with role: {role}")
        return token
    
    def revoke_api_token(self, token: str) -> bool:
        """
        Revoke an API token
        
        Args:
            token: Token to revoke
            
        Returns:
            True if token was revoked, False if not found
        """
        if token in self._valid_tokens:
            del self._valid_tokens[token]
            logger.info(f"Revoked API token")
            return True
        return False
    
    def get_user_from_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Get user information from token
        
        Args:
            token: API token
            
        Returns:
            User information or None if token is invalid
        """
        try:
            return self.validate_api_token(token)
        except HTTPException:
            return None
    
    def check_permission(self, token: str, required_permission: str) -> bool:
        """
        Check if token has required permission
        
        Args:
            token: API token
            required_permission: Required permission
            
        Returns:
            True if permission is granted, False otherwise
        """
        try:
            token_info = self.validate_api_token(token)
            return required_permission in token_info.get('permissions', [])
        except HTTPException:
            return False
    
    def _get_permissions_for_role(self, role: str) -> list:
        """Get permissions for a given role"""
        role_permissions = {
            'admin': ['read', 'write', 'admin', 'delete'],
            'user': ['read', 'write'],
            'readonly': ['read']
        }
        
        return role_permissions.get(role, ['read'])
    
    def cleanup_expired_tokens(self) -> int:
        """
        Clean up expired tokens
        
        Returns:
            Number of tokens cleaned up
        """
        current_time = datetime.utcnow()
        expired_tokens = []
        
        for token, token_info in self._valid_tokens.items():
            if token_info['expires_at'] and current_time > token_info['expires_at']:
                expired_tokens.append(token)
        
        for token in expired_tokens:
            del self._valid_tokens[token]
        
        if expired_tokens:
            logger.info(f"Cleaned up {len(expired_tokens)} expired tokens")
        
        return len(expired_tokens)
    
    def get_token_stats(self) -> Dict[str, Any]:
        """Get statistics about API tokens"""
        total_tokens = len(self._valid_tokens)
        expired_tokens = 0
        admin_tokens = 0
        user_tokens = 0
        
        current_time = datetime.utcnow()
        
        for token_info in self._valid_tokens.values():
            if token_info['expires_at'] and current_time > token_info['expires_at']:
                expired_tokens += 1
            
            if token_info['role'] == 'admin':
                admin_tokens += 1
            else:
                user_tokens += 1
        
        return {
            'total_tokens': total_tokens,
            'expired_tokens': expired_tokens,
            'active_tokens': total_tokens - expired_tokens,
            'admin_tokens': admin_tokens,
            'user_tokens': user_tokens
        }

# Global auth service instance
auth_service = AuthService()

# Dependency for FastAPI endpoints
async def get_current_user(request: Request) -> Dict[str, Any]:
    """
    FastAPI dependency to get current authenticated user
    
    Args:
        request: FastAPI request object
        
    Returns:
        Current user information
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authorization header"
            )
        
        token = auth_header.split(" ")[1]
        return auth_service.validate_api_token(token)
    except HTTPException:
        raise
    except Exception as e:
        logger.warning(f"Authentication failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )

async def require_permission(permission: str, request: Request) -> Dict[str, Any]:
    """
    FastAPI dependency to require specific permission
    
    Args:
        permission: Required permission
        request: FastAPI request object
        
    Returns:
        Current user information if permission is granted
        
    Raises:
        HTTPException: If permission is denied
    """
    try:
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authorization header"
            )
        
        token = auth_header.split(" ")[1]
        if not auth_service.check_permission(token, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required: {permission}"
            )
        return auth_service.validate_api_token(token)
    except HTTPException:
        raise
    except Exception as e:
        logger.warning(f"Permission check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        ) 