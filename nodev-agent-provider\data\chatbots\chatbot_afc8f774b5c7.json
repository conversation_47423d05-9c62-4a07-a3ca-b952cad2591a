{"id": "chatbot_afc8f774b5c7", "website_url": "https://example.com", "company_name": "Example Corp", "config": {"name": "Example Assistant", "description": "AI assistant for Example Corp", "primary_color": "#667eea", "secondary_color": "#764ba2", "logo_url": null, "personality": "professional", "greeting_message": "Hello! How can I help you today?", "fallback_message": "I'm sorry, I don't have information about that. Please try rephrasing your question.", "max_context_length": 8192, "temperature": 0.1, "max_response_length": 500, "custom_knowledge": null, "include_website_content": true, "include_uploaded_documents": true, "business_hours": null, "lead_qualification_enabled": true, "human_handoff_enabled": true}, "status": "error", "created_at": "2025-08-24 08:15:58.044378", "updated_at": "2025-08-24 08:16:15.154033", "vectorstore_path": "qdrant_db/websites/chatbot_afc8f774b5c7", "knowledge_base_size": 0, "last_training": null, "total_conversations": 0, "total_messages": 0, "average_response_time": 0.0, "embed_code": "<!-- Website Chatbot for https://example.com -->\n<div id=\"chatbot-widget-chatbot_afc8f774b5c7\" class=\"chatbot-widget\"></div>\n<script>\n(function() {\n    var script = document.createElement('script');\n    script.src = 'https://yourdomain.com/widget.js?id=chatbot_afc8f774b5c7';\n    script.async = true;\n    document.head.appendChild(script);\n})();\n</script>"}